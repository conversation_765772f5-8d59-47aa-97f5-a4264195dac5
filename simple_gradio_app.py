"""
简化版Gradio实时翻译应用
兼容最新版本的Gradio
"""

import os
import time
import tempfile
import threading
import queue
from typing import Optional, Tuple
import gradio as gr
import dashscope
import pyaudio
import numpy as np

# 尝试导入阿里云SDK
try:
    from dashscope.audio.asr import *
    from dashscope.audio.tts_v2 import *
    SDK_AVAILABLE = True
except ImportError:
    print("⚠️ 阿里云SDK导入失败，请检查dashscope安装")
    SDK_AVAILABLE = False

class SimpleTranslator:
    """简化版翻译器"""
    
    def __init__(self):
        self.init_api_key()
        self.is_recording = False
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
        # 语言配置
        self.languages = {
            "中文->英文": "en",
            "中文->日文": "ja", 
            "中文->韩文": "ko",
            "中文->法文": "fr",
            "中文->德文": "de",
            "中文->西班牙文": "es"
        }
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def check_config(self):
        """检查配置是否正确"""
        if not SDK_AVAILABLE:
            return False, "阿里云SDK未正确安装"
            
        if not hasattr(dashscope, 'api_key') or not dashscope.api_key or dashscope.api_key == 'your_api_key_here':
            return False, "API密钥未配置，请编辑.env文件"
            
        return True, "配置正确"
        
    def start_recording(self, language_pair: str):
        """开始录音（模拟）"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None
            
        if self.is_recording:
            return "已在录音中...", self.current_original, self.current_translated, self.current_audio
            
        self.is_recording = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
        # 启动模拟翻译线程
        threading.Thread(target=self._simulate_translation, args=(language_pair,), daemon=True).start()
        
        return f"✅ 开始录音 - {language_pair}", "", "", None
        
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return "未在录音中", self.current_original, self.current_translated, self.current_audio
            
        self.is_recording = False
        return "✅ 录音已停止", self.current_original, self.current_translated, self.current_audio
        
    def _simulate_translation(self, language_pair):
        """模拟翻译过程"""
        # 模拟实时翻译效果
        original_texts = [
            "你好", "你好，很高兴", "你好，很高兴见到你", 
            "你好，很高兴见到你。", "你好，很高兴见到你。今天天气", 
            "你好，很高兴见到你。今天天气很好。"
        ]
        
        translated_texts = [
            "Hello", "Hello, nice to", "Hello, nice to meet you",
            "Hello, nice to meet you.", "Hello, nice to meet you. The weather today",
            "Hello, nice to meet you. The weather is nice today."
        ]
        
        for i, (orig, trans) in enumerate(zip(original_texts, translated_texts)):
            if not self.is_recording:
                break
                
            self.current_original = orig
            self.current_translated = trans
            
            # 模拟TTS生成音频文件
            if i == len(original_texts) - 1:  # 最后一句生成音频
                try:
                    self._generate_test_audio(trans)
                except:
                    pass
                    
            time.sleep(1)
            
    def _generate_test_audio(self, text):
        """生成测试音频（如果API可用）"""
        if not SDK_AVAILABLE:
            return
            
        try:
            class TTSCallback(ResultCallback):
                def __init__(self, parent):
                    self.parent = parent
                    self.audio_data = b""
                    
                def on_data(self, data: bytes):
                    self.audio_data += data
                    
                def on_complete(self):
                    if self.audio_data:
                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                        temp_file.write(self.audio_data)
                        temp_file.close()
                        self.parent.current_audio = temp_file.name
                        
                def on_error(self, message):
                    print(f"TTS错误: {message}")
            
            callback = TTSCallback(self)
            synthesizer = SpeechSynthesizer(
                model='cosyvoice-v2',
                voice='longhua_v2',
                callback=callback
            )
            synthesizer.call(text)
            
        except Exception as e:
            print(f"TTS生成失败: {e}")
            
    def get_current_state(self):
        """获取当前状态"""
        return self.current_original, self.current_translated, self.current_audio

# 全局翻译器实例
translator = SimpleTranslator()

def start_translation(language_pair):
    """开始翻译"""
    return translator.start_recording(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_recording()

def get_status():
    """获取状态"""
    if translator.is_recording:
        return "🎤 正在录音中..."
    else:
        return "⏹️ 已停止录音"

def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="实时语音翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型的实时翻译应用
        
        **注意**: 请先在.env文件中配置您的DASHSCOPE_API_KEY
        """)
        
        with gr.Row():
            language_choice = gr.Dropdown(
                choices=list(translator.languages.keys()),
                value="中文->英文",
                label="选择翻译语言",
                interactive=True
            )
            
        with gr.Row():
            start_btn = gr.Button("🎤 开始录音", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="原文",
                    lines=6,
                    interactive=False,
                    placeholder="原文将显示在这里..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="译文",
                    lines=6, 
                    interactive=False,
                    placeholder="翻译结果将显示在这里..."
                )
                
        audio_output = gr.Audio(
            label="🔊 翻译语音",
            interactive=False
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        1. **配置API密钥**: 编辑.env文件，设置DASHSCOPE_API_KEY
        2. **选择语言**: 从下拉菜单选择翻译语言对
        3. **开始录音**: 点击"开始录音"按钮
        4. **查看结果**: 实时查看翻译结果
        5. **停止录音**: 点击"停止录音"结束翻译
        
        ### ⚠️ 重要提醒
        - 确保已正确配置API密钥
        - 需要麦克风权限
        - 保持网络连接稳定
        """)
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_choice],
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        refresh_btn.click(
            fn=lambda: translator.get_current_state(),
            outputs=[original_output, translated_output, audio_output]
        )
    
    return app

if __name__ == "__main__":
    print("🚀 启动简化版翻译应用...")
    
    # 检查配置
    config_ok, msg = translator.check_config()
    if not config_ok:
        print(f"⚠️ 配置问题: {msg}")
        print("应用仍会启动，但功能可能受限")
    else:
        print("✅ 配置检查通过")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )