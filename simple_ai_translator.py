"""
简化版AI翻译智能体
基于大模型的智能语音翻译助手
"""

import os
import time
import tempfile
import threading
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
from dashscope import Generation
from dashscope.audio.tts_v2 import *


class SimpleAITranslator:
    """简化版AI翻译智能体"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # AI智能体配置
        self.ai_model = "qwen-turbo"  # 使用通义千问
        self.translation_mode = "智能检测"
        self.context_memory = []  # 对话上下文记忆
        
        # 翻译模式配置
        self.translation_modes = {
            "智能检测": "自动检测语言并智能翻译",
            "中英互译": "中文和英文之间互相翻译",
            "多语言助手": "支持中文、英文、日文、韩文等多语言",
            "商务翻译": "专业商务场景翻译",
            "旅游助手": "旅游场景专用翻译"
        }
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_analysis = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def ai_translate_text(self, text: str, mode: str):
        """AI文本翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, ""
            
        if not text or text.strip() == "":
            return "❌ 请输入要翻译的文本", "", "", None, ""
            
        try:
            # 构建AI提示词
            prompt = self._build_ai_prompt(text.strip(), mode)
            
            # 调用AI大模型
            response = Generation.call(
                model=self.ai_model,
                prompt=prompt,
                temperature=0.3,
                max_tokens=1000
            )
            
            if response.status_code == 200:
                ai_result = response.output.text.strip()
                return self._parse_ai_response(text.strip(), ai_result, mode)
            else:
                return f"❌ AI调用失败: {response}", "", "", None, ""
                
        except Exception as e:
            return f"❌ AI翻译错误: {e}", "", "", None, ""
            
    def _build_ai_prompt(self, text: str, mode: str):
        """构建AI提示词"""
        mode_prompts = {
            "智能检测": f"""
你是一个智能翻译助手。请分析以下文本并完成翻译：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [翻译结果]
分析: [简短的语言和翻译分析]

翻译规则：
- 如果是中文，翻译成英文
- 如果是英文，翻译成中文  
- 如果是其他语言，翻译成中文
- 保持原文的语气和风格
""",
            "中英互译": f"""
你是专业的中英文翻译助手。请翻译以下文本：

文本: "{text}"

请按以下格式回复：
语言: [中文/英文]
翻译: [翻译结果]
分析: [翻译要点说明]

翻译要求：
- 中文翻译成英文，英文翻译成中文
- 保持专业和准确
- 考虑语境和文化差异
""",
            "多语言助手": f"""
你是多语言翻译专家。请处理以下文本：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [翻译成中文]
分析: [语言特点和翻译说明]

支持语言：中文、英文、日文、韩文、法文、德文、西班牙文等
""",
            "商务翻译": f"""
你是专业商务翻译助手。请翻译以下商务文本：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [专业商务翻译]
分析: [商务用语特点说明]

翻译要求：
- 使用正式、专业的商务用语
- 准确传达商务含义
- 符合商务礼仪和习惯
""",
            "旅游助手": f"""
你是旅游翻译助手。请翻译以下旅游相关文本：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [实用旅游翻译]
分析: [旅游场景说明]

翻译要求：
- 简单易懂，便于交流
- 考虑旅游场景的实用性
- 提供友好、礼貌的表达
"""
        }
        
        base_prompt = mode_prompts.get(mode, mode_prompts["智能检测"])
        
        # 添加上下文记忆
        if self.context_memory:
            context = "\n".join(self.context_memory[-3:])  # 最近3条对话
            base_prompt += f"\n\n对话上下文：\n{context}"
        
        return base_prompt
        
    def _parse_ai_response(self, original: str, ai_response: str, mode: str):
        """解析AI响应"""
        try:
            lines = ai_response.split('\n')
            detected_lang = ""
            translation = ""
            analysis = ""
            
            for line in lines:
                line = line.strip()
                if line.startswith('语言:') or line.startswith('Language:'):
                    detected_lang = line.split(':', 1)[1].strip()
                elif line.startswith('翻译:') or line.startswith('Translation:'):
                    translation = line.split(':', 1)[1].strip()
                elif line.startswith('分析:') or line.startswith('Analysis:'):
                    analysis = line.split(':', 1)[1].strip()
            
            # 如果解析失败，使用整个响应作为翻译
            if not translation:
                translation = ai_response
                
            self.current_original = original
            self.current_translated = translation
            self.current_analysis = f"🧠 {detected_lang} | {analysis}" if analysis else f"🧠 AI智能翻译"
            
            print(f"[AI翻译] 语言: {detected_lang}")
            print(f"[AI翻译] 译文: {translation}")
            print(f"[AI分析] {analysis}")
            
            # 添加到对话历史
            self.conversation_history.append({
                'timestamp': time.strftime('%H:%M:%S'),
                'mode': mode,
                'detected_lang': detected_lang,
                'original': original,
                'translated': translation,
                'analysis': analysis
            })
            
            # 添加到上下文记忆
            self.context_memory.append(f"原文: {original} | 译文: {translation}")
            if len(self.context_memory) > 10:
                self.context_memory.pop(0)
            
            # 生成语音
            audio_file = self._generate_tts(translation)
            
            return (
                f"✅ AI翻译完成",
                original,
                translation,
                audio_file,
                self._get_conversation_display()
            )
            
        except Exception as e:
            print(f"AI响应解析错误: {e}")
            return f"❌ 解析错误: {e}", "", "", None, ""
            
    def _generate_tts(self, text: str):
        """生成TTS语音"""
        try:
            class TTSCallback(ResultCallback):
                def __init__(self):
                    self.audio_data = b""
                    
                def on_data(self, data: bytes):
                    self.audio_data += data
                    
                def on_complete(self):
                    pass
                        
                def on_error(self, message):
                    print(f"TTS错误: {message}")
            
            callback = TTSCallback()
            
            synthesizer = SpeechSynthesizer(
                model='cosyvoice-v2',
                voice='longhua_v2',
                callback=callback
            )
            synthesizer.call(text)
            
            # 等待TTS完成
            time.sleep(2)
            
            if callback.audio_data:
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                temp_file.write(callback.audio_data)
                temp_file.close()
                print(f"🔊 AI语音已生成")
                return temp_file.name
            else:
                return None
                
        except Exception as e:
            print(f"TTS生成失败: {e}")
            return None
            
    def _get_conversation_display(self):
        """获取对话历史显示"""
        if not self.conversation_history:
            return "🤖 AI智能对话历史将显示在这里...\n\n系统会自动检测语言并智能翻译，还会提供分析说明！"
            
        display_text = "🤖 AI智能对话记录:\n" + "="*60 + "\n\n"
        
        for i, conv in enumerate(self.conversation_history[-8:], 1):
            display_text += f"[{conv['timestamp']}] 模式: {conv['mode']}\n"
            display_text += f"🧠 检测语言: {conv.get('detected_lang', '未知')}\n"
            display_text += f"🎤 原文: {conv['original']}\n"
            display_text += f"🌐 译文: {conv['translated']}\n"
            if conv.get('analysis'):
                display_text += f"💡 分析: {conv['analysis']}\n"
            display_text += "-" * 50 + "\n\n"
            
        return display_text
        
    def clear_history(self):
        """清空历史记录"""
        self.conversation_history = []
        self.context_memory = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_analysis = ""
        return "✅ 历史记录已清空", "", "", None, "🤖 AI智能对话历史将显示在这里..."

# 全局实例
simple_ai = SimpleAITranslator()

def set_api_key(api_key):
    """设置API密钥"""
    return simple_ai.set_api_key(api_key)

def ai_translate_text(text, mode):
    """AI文本翻译"""
    return simple_ai.ai_translate_text(text, mode)

def clear_history():
    """清空历史"""
    return simple_ai.clear_history()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="简化版AI翻译智能体", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🤖 简化版AI翻译智能体
        
        **🧠 AI驱动**: 基于通义千问大模型的智能翻译助手
        
        **✨ 核心特色**: 
        - 🤖 AI自动语言检测
        - 🧠 智能翻译分析
        - 💭 上下文记忆
        - 🎯 多场景模式
        - 📝 详细分析说明
        - 🔊 语音合成播放
        
        **🌟 简单易用**: 输入文本即可获得AI智能翻译
        """)
        
        # API密钥设置区域
        with gr.Accordion("🔑 API密钥设置", open=not simple_ai.api_key):
            gr.Markdown("""
            ### 🔗 获取API密钥
            1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
            2. 注册/登录阿里云账号
            3. 开通百炼服务（有免费额度）
            4. 创建API密钥
            5. 将密钥粘贴到下方输入框
            """)
            
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    type="password",
                    value=simple_ai.api_key if simple_ai.api_key else ""
                )
                set_key_btn = gr.Button("💾 保存密钥", variant="primary")
                
            key_status = gr.Textbox(
                label="设置状态",
                value="✅ API密钥已配置" if simple_ai.api_key else "⚠️ 请设置API密钥",
                interactive=False
            )
        
        # AI翻译区域
        with gr.Row():
            with gr.Column():
                input_text = gr.Textbox(
                    label="🎤 输入文本",
                    lines=4,
                    placeholder="请输入要翻译的文本...",
                    interactive=True
                )
                
                translation_mode = gr.Dropdown(
                    choices=list(simple_ai.translation_modes.keys()),
                    value="智能检测",
                    label="选择AI翻译模式",
                    interactive=True
                )
                
                with gr.Row():
                    translate_btn = gr.Button("🤖 AI智能翻译", variant="primary", size="lg")
                    clear_btn = gr.Button("🗑️ 清空历史", variant="secondary")
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🤖 AI智能翻译结果",
                    lines=4,
                    interactive=False,
                    placeholder="AI翻译结果将显示在这里..."
                )
                
                # AI分析显示
                analysis_display = gr.Textbox(
                    label="🧠 AI智能分析",
                    lines=2,
                    interactive=False,
                    placeholder="AI分析将显示在这里..."
                )
        
        # 状态显示
        status_display = gr.Textbox(
            label="状态",
            value="AI助手准备就绪",
            interactive=False
        )
        
        # 语音播放
        audio_output = gr.Audio(
            label="🔊 AI语音合成",
            interactive=False,
            autoplay=True
        )
        
        # AI对话历史
        conversation_history = gr.Textbox(
            label="🤖 AI智能对话历史",
            lines=15,
            interactive=False,
            placeholder="AI智能对话历史将显示在这里..."
        )
        
        gr.Markdown("""
        ### 🤖 AI翻译智能体说明
        
        #### 🌟 AI核心优势
        - **🧠 智能理解**: AI大模型深度理解语言和语境
        - **🎯 场景适配**: 根据不同场景提供最佳翻译
        - **💭 上下文记忆**: 记住对话历史，提供连贯翻译
        - **📊 智能分析**: 提供语言特点和翻译要点分析
        - **🔊 语音合成**: 自动生成翻译语音
        
        #### 🎯 翻译模式详解
        - **智能检测**: AI自动检测语言并选择最佳翻译方向
        - **中英互译**: 专业的中英文双向翻译
        - **多语言助手**: 支持多种语言的智能翻译
        - **商务翻译**: 专业商务场景，使用正式商务用语
        - **旅游助手**: 旅游场景专用，简单实用易交流
        
        #### 🚀 使用步骤
        1. **设置API密钥**: 输入您的阿里云API密钥
        2. **输入文本**: 在文本框中输入要翻译的内容
        3. **选择模式**: 根据使用场景选择合适的翻译模式
        4. **开始翻译**: 点击"AI智能翻译"按钮
        5. **查看结果**: 
           - 🌐 查看翻译结果
           - 🧠 查看AI分析说明
           - 🔊 听取语音合成
           - 📝 查看对话历史
        
        #### 💡 使用技巧
        - AI会记住对话上下文，连续翻译效果更好
        - 不同模式适合不同场景，选择合适的模式
        - AI分析会告诉您语言特点和翻译要点
        - 可以随时清空历史记录重新开始
        """)
        
        # 事件绑定
        set_key_btn.click(
            fn=set_api_key,
            inputs=[api_key_input],
            outputs=[key_status]
        )
        
        translate_btn.click(
            fn=ai_translate_text,
            inputs=[input_text, translation_mode],
            outputs=[status_display, input_text, translated_output, audio_output, conversation_history]
        )
        
        clear_btn.click(
            fn=clear_history,
            outputs=[status_display, input_text, translated_output, audio_output, conversation_history]
        )
    
    return app

if __name__ == "__main__":
    print("🤖 启动简化版AI翻译智能体...")
    
    config_ok, msg = simple_ai.check_config()
    if config_ok:
        print("✅ AI配置检查通过")
    else:
        print(f"⚠️ 配置问题: {msg}")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7868,  # 使用新端口
        share=False,
        show_error=True
    )