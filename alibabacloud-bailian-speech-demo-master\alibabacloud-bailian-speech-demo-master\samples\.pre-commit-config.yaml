repos:
  - repo: https://github.com/pre-commit/mirrors-yapf.git
    rev: v0.30.0
    hooks:
      - id: yapf
        exclude: |
            (?x)^(
                tests/data
            )$
  - repo: https://github.com/pre-commit/pre-commit-hooks.git
    rev: v3.1.0
    hooks:
      - id: trailing-whitespace
        exclude: thirdparty/
      - id: check-yaml
        exclude: thirdparty/
      - id: requirements-txt-fixer
        exclude: thirdparty/
      - id: double-quote-string-fixer
        exclude: thirdparty/
      - id: check-merge-conflict
        exclude: thirdparty/
      - id: fix-encoding-pragma
        exclude: thirdparty/
        args: ["--remove"]
      - id: mixed-line-ending
        exclude: thirdparty/
        args: ["--fix=lf"]
