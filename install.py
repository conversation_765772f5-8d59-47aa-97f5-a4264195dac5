"""
自动安装脚本
检查并安装所需依赖
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 开始安装依赖包...")
    
    # 使用国内镜像源
    mirrors = [
        "https://pypi.tuna.tsinghua.edu.cn/simple/",
        "https://mirrors.aliyun.com/pypi/simple/",
        "https://pypi.douban.com/simple/"
    ]
    
    for mirror in mirrors:
        print(f"尝试使用镜像源: {mirror}")
        success, stdout, stderr = run_command(f"pip install -r requirements.txt -i {mirror}")
        if success:
            print("✅ 依赖安装成功")
            return True
        else:
            print(f"❌ 使用镜像源 {mirror} 安装失败")
            print(f"错误信息: {stderr}")
    
    # 如果所有镜像都失败，尝试默认源
    print("尝试使用默认源...")
    success, stdout, stderr = run_command("pip install -r requirements.txt")
    if success:
        print("✅ 依赖安装成功")
        return True
    else:
        print("❌ 依赖安装失败")
        print(f"错误信息: {stderr}")
        return False

def check_audio_system():
    """检查音频系统"""
    print("🔊 检查音频系统...")
    
    try:
        import pyaudio
        # 尝试初始化PyAudio
        p = pyaudio.PyAudio()
        device_count = p.get_device_count()
        print(f"✅ 检测到 {device_count} 个音频设备")
        
        # 查找默认输入设备
        default_input = p.get_default_input_device_info()
        print(f"默认输入设备: {default_input['name']}")
        
        p.terminate()
        return True
    except Exception as e:
        print(f"❌ 音频系统检查失败: {e}")
        print("可能需要安装音频驱动或检查麦克风权限")
        return False

def create_env_file():
    """创建环境配置文件"""
    if not os.path.exists('.env'):
        print("📝 创建环境配置文件...")
        with open('.env', 'w', encoding='utf-8') as f:
            f.write('# 阿里云百炼API密钥配置\n')
            f.write('# 请将下面的your_api_key_here替换为您的实际API密钥\n')
            f.write('# 获取方式: https://dashscope.console.aliyun.com/\n')
            f.write('DASHSCOPE_API_KEY=your_api_key_here\n')
        print("✅ 已创建.env配置文件")
        print("请编辑.env文件，设置您的DASHSCOPE_API_KEY")
    else:
        print("✅ .env文件已存在")

def main():
    """主安装流程"""
    print("🚀 开始安装实时翻译应用...")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 检查音频系统
    check_audio_system()
    
    # 创建配置文件
    create_env_file()
    
    print("=" * 50)
    print("🎉 安装完成！")
    print("\n📋 下一步操作:")
    print("1. 编辑.env文件，设置您的DASHSCOPE_API_KEY")
    print("2. 运行: python run_app.py")
    print("3. 或直接运行: python simple_translator.py")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")