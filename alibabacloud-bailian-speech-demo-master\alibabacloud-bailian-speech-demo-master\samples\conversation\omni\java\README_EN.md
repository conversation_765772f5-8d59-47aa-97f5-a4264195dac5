Omni-Realtime Full-Modal Large Model Example

English | [简体中文](./README.md)

This example demonstrates how to use the Omni-Realtime full-modal large model API, including audio interaction with interruption support and video interaction.

## Prerequisites

### Configure Alibaba Cloud Bailian API-KEY
Before using the Bailian SDK to call the model, you need to create a model service on Alibaba Cloud's console and obtain an API-KEY.
- On the [Bailian Console](https://bailian.console.aliyun.com/), find the API-KEY entry in the lower left corner and click to enter the management page.
- Click 【Create New API-KEY】 to automatically generate an API-KEY for this account. The list will display the encrypted API-KEY. Click 【View】 to see the plaintext information of the API-KEY. Please save the plaintext information as it will be needed for subsequent API-KEY usage.
- For more configuration details about Bailian, please refer to: [PREREQUISITES.md](../../../../PREREQUISITES.md)

### 1. Audio Interaction Example
The audio interaction example will record your voice input via the microphone and play the audio response from the Omni model through the speaker. You can interrupt the model's output at any time. To avoid self-interruption when playing the model's output, we recommend using headphones for the experience.
```shell
cd run_server_vad
./run.sh
```

### 2. Video Interaction Example
The video interaction example will record your voice input via the microphone and capture video input via the webcam. It will play the audio response from the Omni model through the speaker.

You can interrupt the model's output at any time. To avoid self-interruption when playing the model's output, we recommend using headphones for the experience.
1. Run the service
```shell
cd run_with_camera
./run.sh
```
At this point, video capture has not started yet; only voice interaction is active.

2. Open the web frontend to access the camera
After initiating the interaction, double-click to open the `interface/index.html` page and grant permission for the camera. Click the 【Create Connection】 button to send the video stream to the Python program and start transmitting the video.

    You can open or close the connection in the web page at any time during the voice interaction to start or end the video transmission.

### 3. Non-server_vad Mode Example

This example simulates the operation without using the `server_vad` mode, actively controlling the timing of the model's responses. Running the example will simulate multiple rounds of conversation, playing the audio generated by the large model through the speaker. It waits until the previous response has finished playing before starting the next task.

```shell
cd run_without_server_vad
./run.sh
```

### About the Player

In `b64_pcm_player.py`, we implemented a streaming player that supports interruptions using pyaudio. The input of the player is base 64 encoded PCM format audio.

There are two working threads in the player. One thread is responsible for reading base64 encoded data and decoding it, while the other thread plays the audio in segments according to the `chunk_size`. Audio segment playback is blocking, so a larger `chunk_size` may affect the latency of interruption. It is recommended to configure it as 100ms.
