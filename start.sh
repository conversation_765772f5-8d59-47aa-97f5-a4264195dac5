#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 启动实时翻译应用...${NC}"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 未找到Python，请先安装Python 3.8+${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ 找到Python: $($PYTHON_CMD --version)${NC}"

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo -e "${RED}❌ Python版本过低，需要3.8+，当前版本: $PYTHON_VERSION${NC}"
    exit 1
fi

# 检查是否已安装依赖
echo "📦 检查依赖..."
$PYTHON_CMD -c "import dashscope, gradio, pyaudio" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️  检测到缺少依赖，开始自动安装...${NC}"
    $PYTHON_CMD install.py
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
fi

# 检查API密钥配置
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  未找到.env配置文件，正在创建...${NC}"
    cat > .env << EOF
# 阿里云百炼API密钥配置
# 请将下面的your_api_key_here替换为您的实际API密钥
# 获取方式: https://dashscope.console.aliyun.com/
DASHSCOPE_API_KEY=your_api_key_here
EOF
    echo
    echo -e "${YELLOW}📝 请编辑.env文件，设置您的DASHSCOPE_API_KEY${NC}"
    echo -e "${YELLOW}然后重新运行此脚本${NC}"
    exit 1
fi

# 检查API密钥是否已配置
if grep -q "your_api_key_here" .env; then
    echo -e "${YELLOW}⚠️  请先在.env文件中配置您的DASHSCOPE_API_KEY${NC}"
    exit 1
fi

# 启动应用
echo -e "${GREEN}✅ 配置检查完成，启动应用...${NC}"
$PYTHON_CMD simple_translator.py