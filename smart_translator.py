"""
智能自动双向翻译应用
自动检测语言并切换翻译方向，无需手动干预
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SmartTranslator:
    """智能自动双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.auto_mode = True  # 自动模式
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 智能语言检测
        self.last_detected_language = None
        self.language_confidence = {}
        self.detection_history = []
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None  # A语言识别器
        self.recognizer_B = None  # B语言识别器
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800  # 提高静音阈值
        self.min_speech_duration = 0.8  # 最小语音持续时间
        self.speech_start_time = None
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.last_detected_language = None
        self.language_confidence = {self.lang_A: 0, self.lang_B: 0}
        self.detection_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程 - 增强的防回环机制"""
        print("🎤 开始智能录音...")
        silence_count = 0
        speech_buffer = []
        is_speaking = False
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
"""
智能自动双向翻译应用
自动检测语言并切换翻译方向，无需手动干预
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SmartTranslator:
    """智能自动双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.auto_mode = True  # 自动模式
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 智能语言检测
        self.last_detected_language = None
        self.language_confidence = {}
        self.detection_history = []
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None  # A语言识别器
        self.recognizer_B = None  # B语言识别器
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800  # 提高静音阈值
        self.min_speech_duration = 0.8  # 最小语音持续时间
        self.speech_start_time = None
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.last_detected_language = None
        self.language_confidence = {self.lang_A: 0, self.lang_B: 0}
        self.detection_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程 - 增强的防回环机制"""
        print("🎤 开始智能录音...")
        silence_count = 0
        speech_buffer = []
        is_speaking = False
        
        while self.is_translating:
                # 计算音频能量 - 修复数学错误
                audio_data = np.frombuffer(data, dtype=np.int16)
                if len(audio_data) > 0:
                    energy = np.sqrt(np.mean(np.abs(audio_data.astype(np.float32))**2))
                else:
                    energy = 0
"""
智能自动双向翻译应用
自动检测语言并切换翻译方向，无需手动干预
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SmartTranslator:
    """智能自动双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.auto_mode = True  # 自动模式
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 智能语言检测
        self.last_detected_language = None
        self.language_confidence = {}
        self.detection_history = []
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None  # A语言识别器
        self.recognizer_B = None  # B语言识别器
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800  # 提高静音阈值
        self.min_speech_duration = 0.8  # 最小语音持续时间
        self.speech_start_time = None
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.last_detected_language = None
        self.language_confidence = {self.lang_A: 0, self.lang_B: 0}
        self.detection_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程 - 增强的防回环机制"""
        print("🎤 开始智能录音...")
        silence_count = 0
        speech_buffer = []
        is_speaking = False
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
"""
智能自动双向翻译应用
自动检测语言并切换翻译方向，无需手动干预
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SmartTranslator:
    """智能自动双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.auto_mode = True  # 自动模式
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 智能语言检测
        self.last_detected_language = None
        self.language_confidence = {}
        self.detection_history = []
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None  # A语言识别器
        self.recognizer_B = None  # B语言识别器
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800  # 提高静音阈值
        self.min_speech_duration = 0.8  # 最小语音持续时间
        self.speech_start_time = None
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.last_detected_language = None
        self.language_confidence = {self.lang_A: 0, self.lang_B: 0}
        self.detection_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程 - 增强的防回环机制"""
        print("🎤 开始智能录音...")
        silence_count = 0
        speech_buffer = []
        is_speaking = False
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
                # 计算音频能量 - 修复数学错误
                audio_data = np.frombuffer(data, dtype=np.int16)
                if len(audio_data) > 0:
                    energy = np.sqrt(np.mean(np.abs(audio_data.astype(np.float32))**2))
                else:
                    energy = 0
"""
智能自动双向翻译应用
自动检测语言并切换翻译方向，无需手动干预
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SmartTranslator:
    """智能自动双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.auto_mode = True  # 自动模式
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 智能语言检测
        self.last_detected_language = None
        self.language_confidence = {}
        self.detection_history = []
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None  # A语言识别器
        self.recognizer_B = None  # B语言识别器
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800  # 提高静音阈值
        self.min_speech_duration = 0.8  # 最小语音持续时间
        self.speech_start_time = None
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.last_detected_language = None
        self.language_confidence = {self.lang_A: 0, self.lang_B: 0}
        self.detection_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程 - 增强的防回环机制"""
        print("🎤 开始智能录音...")
        silence_count = 0
        speech_buffer = []
        is_speaking = False
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
"""
智能自动双向翻译应用
自动检测语言并切换翻译方向，无需手动干预
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SmartTranslator:
    """智能自动双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.auto_mode = True  # 自动模式
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 智能语言检测
        self.last_detected_language = None
        self.language_confidence = {}
        self.detection_history = []
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None  # A语言识别器
        self.recognizer_B = None  # B语言识别器
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800  # 提高静音阈值
        self.min_speech_duration = 0.8  # 最小语音持续时间
        self.speech_start_time = None
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.last_detected_language = None
        self.language_confidence = {self.lang_A: 0, self.lang_B: 0}
        self.detection_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程 - 增强的防回环机制"""
        print("🎤 开始智能录音...")
        silence_count = 0
        speech_buffer = []
        is_speaking = False
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
                # 计算音频能量
                audio_data = np.frombuffer(data, dtype=np.int16)
                energy = np.sqrt(np.mean(audio_data**2))
                
                if energy > self.silence_threshold:
                    if not is_speaking:
                        is_speaking = True
                        self.speech_start_time = time.time()
                        speech_buffer = []
                        print("🗣️ 检测到语音开始")
                    
                    silence_count = 0
                    speech_buffer.append(data)
                    
                    # 发送音频数据到识别器
                    self._send_to_recognizers(data)
                    
                else:
                    silence_count += 1
                    
                    if is_speaking:
                        speech_buffer.append(data)
                        
                        # 静音时间足够长，认为语音结束
                        if silence_count > 15:  # 约1.5秒静音
                            speech_duration = time.time() - self.speech_start_time if self.speech_start_time else 0
                            
                            if speech_duration >= self.min_speech_duration:
                                print(f"🗣️ 语音结束，持续时间: {speech_duration:.1f}秒")
                            
                            is_speaking = False
                            speech_buffer = []
                            silence_count = 0
                        else:
                            # 短暂静音，继续发送
                            self._send_to_recognizers(data)
                            
            except Exception as e:
                print(f"智能录音错误: {e}")
                break
                
    def _send_to_recognizers(self, audio_data):
        """发送音频数据到双识别器"""
        try:
            if self.recognizer_A:
                self.recognizer_A.send_audio_frame(audio_data)
            if self.recognizer_B:
                self.recognizer_B.send_audio_frame(audio_data)
        except Exception as e:
            print(f"发送音频到识别器错误: {e}")
            
    def _start_dual_recognition(self):
        """启动双语言识别器"""
        try:
            # 创建A语言识别器
            self._create_recognizer_A()
            
            # 创建B语言识别器  
            self._create_recognizer_B()
            
        except Exception as e:
            print(f"双识别器启动失败: {e}")
            
    def _create_recognizer_A(self):
        """创建A语言识别器"""
        class RecognizerACallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.lang_code = parent.lang_A
                self.lang_name = parent.lang_A_name
                
            def on_open(self):
                print(f"🌐 {self.lang_name}识别器已连接")
                
            def on_close(self):
                print(f"🌐 {self.lang_name}识别器已断开")
                
            def on_error(self, message):
                print(f"❌ {self.lang_name}识别错误: {message}")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    if transcription_result and transcription_result.text:
                        # 更新语言置信度
                        self.parent._update_language_confidence(self.lang_code, transcription_result.text)
                        
                        # 如果这是当前检测到的主要语言
                        if self.parent._is_primary_language(self.lang_code):
                            self.parent.current_original = transcription_result.text
                            print(f"[{self.lang_name}] 原文: {transcription_result.text}")
                            
                            # 处理翻译结果
                            if translation_result:
                                translation = translation_result.get_translation(self.parent.lang_B)
                                if translation and translation.text:
                                    self.parent.current_translated = translation.text
                                    self.parent.current_direction = f"{self.lang_name} → {self.parent.lang_B_name}"
                                    print(f"[{self.lang_name}→{self.parent.lang_B_name}] 译文: {translation.text}")
                                    
                                    if translation.is_sentence_end:
                                        self._handle_translation_complete(
                                            transcription_result.text,
                                            translation.text,
                                            self.lang_name,
                                            self.parent.lang_B_name
                                        )
                                        
                except Exception as e:
                    print(f"{self.lang_name}识别事件处理错误: {e}")
                    
            def _handle_translation_complete(self, original, translated, source_lang, target_lang):
                """处理翻译完成"""
                # 添加到对话历史
                self.parent.conversation_history.append({
                    'timestamp': time.strftime('%H:%M:%S'),
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'original': original,
                    'translated': translated
                })
                
                # 生成语音
                self._generate_and_play_tts(translated, self.parent.lang_B)
                
            def _generate_and_play_tts(self, text, target_lang):
                """生成并播放TTS"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = True
                                
                                try:
                                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                    temp_file.write(self.audio_data)
                                    temp_file.close()
                                    self.parent.current_audio = temp_file.name
                                    print(f"🔊 {target_lang}语音已生成")
                                    
                                    # 延迟恢复录音
                                    threading.Thread(target=self._reset_play_state, daemon=True).start()
                                    
                                except Exception as e:
                                    print(f"音频处理错误: {e}")
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = False
                                        
                        def _reset_play_state(self):
                            """重置播放状态"""
                            time.sleep(4)  # 等待音频播放完成
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                            print("🔊 音频播放完成，恢复智能录音")
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                    
                    callback = TTSCallback(self.parent)
                    voice = self._get_voice_for_language(target_lang)
                    
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice=voice,
                        callback=callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
                    with self.parent.audio_play_lock:
                        self.parent.is_playing_audio = False
                        
            def _get_voice_for_language(self, lang_code):
                """根据语言选择声音"""
                voice_map = {
                    'zh': 'longhua_v2',
                    'en': 'longhua_v2', 
                    'ja': 'longhua_v2',
                    'ko': 'longhua_v2',
                    'fr': 'longhua_v2',
                    'de': 'longhua_v2',
                    'es': 'longhua_v2'
                }
                return voice_map.get(lang_code, 'longhua_v2')
        
        try:
            callback = RecognizerACallback(self)
            
            self.recognizer_A = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.lang_B],
                callback=callback,
            )
            
            self.recognizer_A.start()
            print(f"🚀 {self.lang_A_name}识别器已启动")
            
        except Exception as e:
            print(f"{self.lang_A_name}识别器启动失败: {e}")
            
    def _create_recognizer_B(self):
        """创建B语言识别器"""
        class RecognizerBCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.lang_code = parent.lang_B
                self.lang_name = parent.lang_B_name
                
            def on_open(self):
                print(f"🌐 {self.lang_name}识别器已连接")
                
            def on_close(self):
                print(f"🌐 {self.lang_name}识别器已断开")
                
            def on_error(self, message):
                print(f"❌ {self.lang_name}识别错误: {message}")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    if transcription_result and transcription_result.text:
                        # 更新语言置信度
                        self.parent._update_language_confidence(self.lang_code, transcription_result.text)
                        
                        # 如果这是当前检测到的主要语言
                        if self.parent._is_primary_language(self.lang_code):
                            self.parent.current_original = transcription_result.text
                            print(f"[{self.lang_name}] 原文: {transcription_result.text}")
                            
                            # 处理翻译结果
                            if translation_result:
                                translation = translation_result.get_translation(self.parent.lang_A)
                                if translation and translation.text:
                                    self.parent.current_translated = translation.text
                                    self.parent.current_direction = f"{self.lang_name} → {self.parent.lang_A_name}"
                                    print(f"[{self.lang_name}→{self.parent.lang_A_name}] 译文: {translation.text}")
                                    
                                    if translation.is_sentence_end:
                                        self._handle_translation_complete(
                                            transcription_result.text,
                                            translation.text,
                                            self.lang_name,
                                            self.parent.lang_A_name
                                        )
                                        
                except Exception as e:
                    print(f"{self.lang_name}识别事件处理错误: {e}")
                    
            def _handle_translation_complete(self, original, translated, source_lang, target_lang):
                """处理翻译完成"""
                # 添加到对话历史
                self.parent.conversation_history.append({
                    'timestamp': time.strftime('%H:%M:%S'),
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'original': original,
                    'translated': translated
                })
                
                # 生成语音
                self._generate_and_play_tts(translated, self.parent.lang_A)
                
            def _generate_and_play_tts(self, text, target_lang):
                """生成并播放TTS"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = True
                                
                                try:
                                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                    temp_file.write(self.audio_data)
                                    temp_file.close()
                                    self.parent.current_audio = temp_file.name
                                    print(f"🔊 {target_lang}语音已生成")
                                    
                                    # 延迟恢复录音
                                    threading.Thread(target=self._reset_play_state, daemon=True).start()
                                    
                                except Exception as e:
                                    print(f"音频处理错误: {e}")
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = False
                                        
                        def _reset_play_state(self):
                            """重置播放状态"""
                            time.sleep(4)  # 等待音频播放完成
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                            print("🔊 音频播放完成，恢复智能录音")
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                    
                    callback = TTSCallback(self.parent)
                    voice = self._get_voice_for_language(target_lang)
                    
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice=voice,
                        callback=callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
                    with self.parent.audio_play_lock:
                        self.parent.is_playing_audio = False
                        
            def _get_voice_for_language(self, lang_code):
                """根据语言选择声音"""
                voice_map = {
                    'zh': 'longhua_v2',
                    'en': 'longhua_v2', 
                    'ja': 'longhua_v2',
                    'ko': 'longhua_v2',
                    'fr': 'longhua_v2',
                    'de': 'longhua_v2',
                    'es': 'longhua_v2'
                }
                return voice_map.get(lang_code, 'longhua_v2')
        
        try:
            callback = RecognizerBCallback(self)
            
            self.recognizer_B = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.lang_A],
                callback=callback,
            )
            
            self.recognizer_B.start()
            print(f"🚀 {self.lang_B_name}识别器已启动")
            
        except Exception as e:
            print(f"{self.lang_B_name}识别器启动失败: {e}")
            
    def _update_language_confidence(self, lang_code, text):
        """更新语言置信度"""
        if not text or len(text.strip()) < 2:
            return
            
        # 简单的语言检测逻辑
        confidence = len(text.strip())  # 文本长度作为置信度
        
        # 更新置信度
        if lang_code not in self.language_confidence:
            self.language_confidence[lang_code] = 0
            
        self.language_confidence[lang_code] = confidence
        
        # 记录检测历史
        self.detection_history.append({
            'timestamp': time.time(),
            'language': lang_code,
            'confidence': confidence,
            'text': text
        })
        
        # 只保留最近10条记录
        if len(self.detection_history) > 10:
            self.detection_history = self.detection_history[-10:]
            
    def _is_primary_language(self, lang_code):
        """判断是否为主要检测语言"""
        if not self.language_confidence:
            return True
            
        # 获取最高置信度的语言
        max_confidence = max(self.language_confidence.values())
        current_confidence = self.language_confidence.get(lang_code, 0)
        
        # 如果当前语言置信度最高，则为主要语言
        return current_confidence >= max_confidence
        
    def _get_conversation_display(self):
        """获取对话历史显示"""
        if not self.conversation_history:
            return "🤖 智能对话历史将显示在这里...\n\n系统会自动检测语言并切换翻译方向，无需手动操作！"
            
        display_text = "🤖 智能对话记录:\n" + "="*50 + "\n\n"
        
        for i, conv in enumerate(self.conversation_history[-10:], 1):
            display_text += f"[{conv['timestamp']}] {conv['source_lang']} → {conv['target_lang']}\n"
            display_text += f"🎤 原文: {conv['original']}\n"
            display_text += f"🌐 译文: {conv['translated']}\n"
            display_text += "-" * 40 + "\n\n"
            
        return display_text
        
    def get_current_results(self):
        """获取当前结果"""
        return self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction

# 全局实例
translator = SmartTranslator()

def set_api_key(api_key):
    """设置API密钥"""
    return translator.set_api_key(api_key)

def start_smart_translation(language_pair):
    """开始智能翻译"""
    return translator.start_smart_translation(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_translation()

def refresh_results():
    """刷新结果"""
    return translator.get_current_results()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="智能自动双向翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🤖 智能自动双向翻译器
        
        **🎯 核心特色**: 全自动语言检测，无需手动切换方向！
        
        **✨ 智能功能**: 
        - 🧠 自动检测说话语言
        - 🔄 智能切换翻译方向  
        - 🎤 连续语音捕获
        - 🔊 防回环播放
        - 📝 完整对话记录
        
        **🌍 适用场景**: 面对面跨语言自然对话
        """)
        
        # API密钥设置区域
        with gr.Accordion("🔑 API密钥设置", open=not translator.api_key):
            gr.Markdown("""
            ### 🔗 获取API密钥
            1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
            2. 注册/登录阿里云账号
            3. 开通百炼服务（有免费额度）
            4. 创建API密钥
            5. 将密钥粘贴到下方输入框
            """)
            
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    type="password",
                    value=translator.api_key if translator.api_key else ""
                )
                set_key_btn = gr.Button("💾 保存密钥", variant="primary")
                
            key_status = gr.Textbox(
                label="设置状态",
                value="✅ API密钥已配置" if translator.api_key else "⚠️ 请设置API密钥",
                interactive=False
            )
        
        # 智能翻译设置区域
        with gr.Row():
            language_pair = gr.Dropdown(
                choices=list(translator.language_pairs.keys()),
                value="中文 ⇄ 英文",
                label="选择语言对",
                interactive=True
            )
        
        # 控制按钮
        with gr.Row():
            start_btn = gr.Button("🤖 开始智能翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止翻译", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        # 当前翻译方向显示
        direction_display = gr.Textbox(
            label="🧠 智能检测方向",
            value="等待开始...",
            interactive=False
        )
        
        # 实时翻译结果
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 当前原文",
                    lines=4,
                    interactive=False,
                    placeholder="说话时原文将自动显示..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 当前译文",
                    lines=4,
                    interactive=False,
                    placeholder="翻译结果将自动显示..."
                )
        
        # 语音播放
        audio_output = gr.Audio(
            label="🔊 翻译语音（自动播放）",
            interactive=False,
            autoplay=True
        )
        
        # 智能对话历史
        conversation_history = gr.Textbox(
            label="🤖 智能对话历史",
            lines=12,
            interactive=False,
            placeholder="智能对话历史将显示在这里..."
        )
        
        gr.Markdown("""
        ### 🤖 智能翻译说明
        
        #### ✨ 核心优势
        - **🧠 全自动**: 无需手动切换，系统自动检测语言并翻译
        - **🎤 连续对话**: 持续监听，自然对话体验
        - **🔊 防回环**: 智能防止语音循环，播放时暂停录音
        - **📝 完整记录**: 自动记录完整对话历史
        
        #### 🚀 使用步骤
        1. **设置API密钥**: 在上方输入您的阿里云API密钥
        2. **选择语言对**: 选择需要翻译的语言对（如"中文 ⇄ 英文"）
        3. **开始翻译**: 点击"开始智能翻译"按钮
        4. **自然对话**: 
           - 🗣️ 直接对着麦克风说话（任意语言）
           - 🤖 系统自动检测语言并翻译
           - 🔊 自动播放翻译语音
           - 🔄 对方说话时系统自动切换方向
        5. **查看历史**: 在对话历史区域查看完整记录
        6. **结束对话**: 点击"停止翻译"结束
        
        #### 🎯 适用场景
        - **🌍 国际商务**: 中外商务人员面对面交流
        - **✈️ 出国旅游**: 与当地人自然沟通
        - **🤝 文化交流**: 跨文化友好交流
        - **📚 语言学习**: 实时翻译辅助学习
        
        #### 💡 使用技巧
        - 说话时保持清晰，适当停顿让系统识别句子边界
        - 等待翻译语音播放完成后再继续说话
        - 系统会自动检测主要语言，无需担心方向问题
        - 定期点击"刷新结果"查看最新状态
        
        #### 🛡️ 技术保障
        - **智能检测**: 基于阿里云语音大模型的多语言识别
        - **防回环**: 多重机制防止语音循环干扰
        - **低延迟**: 流式处理，响应迅速
        - **高准确率**: 企业级语音识别和翻译质量
        """)
        
        # 事件绑定
        set_key_btn.click(
            fn=set_api_key,
            inputs=[api_key_input],
            outputs=[key_status]
        )
        
        start_btn.click(
            fn=start_smart_translation,
            inputs=[language_pair],
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history, direction_display]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history, direction_display]
        )
        
        refresh_btn.click(
            fn=refresh_results,
            outputs=[original_output, translated_output, audio_output, conversation_history, direction_display]
        )
    
    return app

if __name__ == "__main__":
    print("🤖 启动智能自动双向翻译应用...")
    
    config_ok, msg = translator.check_config()
    if config_ok:
        print("✅ 配置检查通过")
    else:
        print(f"⚠️ 配置问题: {msg}")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7864,  # 使用新端口
        share=False,
        show_error=True
    )
