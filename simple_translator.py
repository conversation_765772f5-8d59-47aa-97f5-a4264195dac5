"""
简化版实时翻译应用
使用Gradio构建，更容易部署和使用
"""

import os
import time
import tempfile
import threading
import queue
from typing import Optional, Tuple
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SimpleTranslator:
    """简化版翻译器"""
    
    def __init__(self):
        self.init_api_key()
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 音频配置
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.languages = {
            "中文->英文": "en",
            "中文->日文": "ja", 
            "中文->韩文": "ko",
            "中文->法文": "fr",
            "中文->德文": "de",
            "中文->西班牙文": "es"
        }
        
        self.pyaudio_obj = None
        self.audio_stream = None
        self.translator = None
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            dashscope.api_key = line.split('=')[1].strip()
                            break
            except FileNotFoundError:
                pass
                
    def start_recording(self, language_pair: str):
        """开始录音"""
        if self.is_recording:
            return "已在录音中..."
            
        if language_pair not in self.languages:
            return "不支持的语言对"
            
        self.is_recording = True
        self.target_language = self.languages[language_pair]
        
        # 启动录音线程
        self.record_thread = threading.Thread(target=self._record_worker)
        self.record_thread.daemon = True
        self.record_thread.start()
        
        # 启动翻译线程
        self.translate_thread = threading.Thread(target=self._translate_worker)
        self.translate_thread.daemon = True
        self.translate_thread.start()
        
        return f"开始录音 - {language_pair}"
        
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return "未在录音中"
            
        self.is_recording = False
        
        # 清理资源
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
            self.audio_stream = None
            
        if self.pyaudio_obj:
            self.pyaudio_obj.terminate()
            self.pyaudio_obj = None
            
        if self.translator:
            self.translator.stop()
            self.translator = None
            
        return "录音已停止"
        
    def _record_worker(self):
        """录音工作线程"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            while self.is_recording:
                try:
                    data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_queue.put(data)
                except Exception as e:
                    print(f"录音错误: {e}")
                    break
                    
        except Exception as e:
            print(f"录音初始化错误: {e}")
            
    def _translate_worker(self):
        """翻译工作线程"""
        class TranslateCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                
            def on_open(self):
                print("翻译器已连接")
                
            def on_close(self):
                print("翻译器已断开")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    original_text = ""
                    translated_text = ""
                    
                    # 处理原文
                    if transcription_result and transcription_result.words:
                        for word in transcription_result.words:
                            if word.fixed:
                                original_text += word.text
                                
                    # 处理翻译
                    if translation_result:
                        translation = translation_result.get_translation(self.parent.target_language)
                        if translation and translation.words:
                            for word in translation.words:
                                if word.fixed:
                                    translated_text += word.text
                                    
                            # 如果句子结束，生成语音
                            if translation.is_sentence_end and translated_text.strip():
                                self._generate_speech(translated_text.strip())
                                
                    if original_text or translated_text:
                        self.parent.result_queue.put({
                            'original': original_text,
                            'translated': translated_text,
                            'timestamp': time.time()
                        })
                        
                except Exception as e:
                    print(f"翻译回调错误: {e}")
                    
            def _generate_speech(self, text):
                """生成语音"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self):
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                # 保存临时音频文件
                                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                temp_file.write(self.audio_data)
                                temp_file.close()
                                
                                # 添加到结果队列
                                self.parent.result_queue.put({
                                    'audio_file': temp_file.name,
                                    'timestamp': time.time()
                                })
                    
                    tts_callback = TTSCallback()
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=tts_callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS错误: {e}")
        
        try:
            callback = TranslateCallback(self)
            
            self.translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.target_language],
                callback=callback,
            )
            
            self.translator.start()
            
            while self.is_recording:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        self.translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频错误: {e}")
                    
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            
    def get_results(self):
        """获取翻译结果"""
        results = []
        while not self.result_queue.empty():
            try:
                result = self.result_queue.get_nowait()
                results.append(result)
            except queue.Empty:
                break
        return results


# 全局翻译器实例
translator = SimpleTranslator()

# 全局状态
current_original = ""
current_translated = ""
current_audio = None

def start_translation(language_pair):
    """开始翻译"""
    global current_original, current_translated, current_audio
    current_original = ""
    current_translated = ""
    current_audio = None
    
    status = translator.start_recording(language_pair)
    return status, "", "", None

def stop_translation():
    """停止翻译"""
    status = translator.stop_recording()
    return status

def update_results():
    """更新结果显示"""
    global current_original, current_translated, current_audio
    
    if not translator.is_recording:
        return current_original, current_translated, current_audio
        
    results = translator.get_results()
    
    for result in results:
        if 'original' in result and result['original']:
            current_original += result['original']
        if 'translated' in result and result['translated']:
            current_translated += result['translated']
        if 'audio_file' in result:
            current_audio = result['audio_file']
            
    return current_original, current_translated, current_audio

def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="实时语音翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型的实时翻译应用
        """)
        
        with gr.Row():
            language_choice = gr.Dropdown(
                choices=list(translator.languages.keys()),
                value="中文->英文",
                label="选择翻译语言",
                interactive=True
            )
            
        with gr.Row():
            start_btn = gr.Button("🎤 开始录音", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="原文",
                    lines=6,
                    interactive=False,
                    placeholder="原文将显示在这里..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="译文",
                    lines=6, 
                    interactive=False,
                    placeholder="翻译结果将显示在这里..."
                )
                
        audio_output = gr.Audio(
            label="🔊 翻译语音",
            interactive=False,
            autoplay=True
        )
        
        gr.Markdown("""
        ### 使用说明
        1. 选择翻译语言对
        2. 点击"开始录音"按钮
        3. 对着麦克风说话
        4. 系统会实时显示翻译结果并播放语音
        5. 点击"停止录音"结束翻译
        """)
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_choice],
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display]
        )
        
        # 定时更新结果 - 使用新的Gradio语法
        def auto_update():
            import time
            while True:
                time.sleep(1)
                yield update_results()
        
        # 创建定时器组件
        timer = gr.Timer(1.0)
        timer.tick(
            fn=update_results,
            outputs=[original_output, translated_output, audio_output]
        )
"""
简化版实时翻译应用
使用Gradio构建，更容易部署和使用
"""

import os
import time
import tempfile
import threading
import queue
from typing import Optional, Tuple
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class SimpleTranslator:
    """简化版翻译器"""
    
    def __init__(self):
        self.init_api_key()
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 音频配置
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.languages = {
            "中文->英文": "en",
            "中文->日文": "ja", 
            "中文->韩文": "ko",
            "中文->法文": "fr",
            "中文->德文": "de",
            "中文->西班牙文": "es"
        }
        
        self.pyaudio_obj = None
        self.audio_stream = None
        self.translator = None
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            dashscope.api_key = line.split('=')[1].strip()
                            break
            except FileNotFoundError:
                pass
                
    def start_recording(self, language_pair: str):
        """开始录音"""
        if self.is_recording:
            return "已在录音中..."
            
        if language_pair not in self.languages:
            return "不支持的语言对"
            
        self.is_recording = True
        self.target_language = self.languages[language_pair]
        
        # 启动录音线程
        self.record_thread = threading.Thread(target=self._record_worker)
        self.record_thread.daemon = True
        self.record_thread.start()
        
        # 启动翻译线程
        self.translate_thread = threading.Thread(target=self._translate_worker)
        self.translate_thread.daemon = True
        self.translate_thread.start()
        
        return f"开始录音 - {language_pair}"
        
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return "未在录音中"
            
        self.is_recording = False
        
        # 清理资源
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
            self.audio_stream = None
            
        if self.pyaudio_obj:
            self.pyaudio_obj.terminate()
            self.pyaudio_obj = None
            
        if self.translator:
            self.translator.stop()
            self.translator = None
            
        return "录音已停止"
        
    def _record_worker(self):
        """录音工作线程"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            while self.is_recording:
                try:
                    data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_queue.put(data)
                except Exception as e:
                    print(f"录音错误: {e}")
                    break
                    
        except Exception as e:
            print(f"录音初始化错误: {e}")
            
    def _translate_worker(self):
        """翻译工作线程"""
        class TranslateCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                
            def on_open(self):
                print("翻译器已连接")
                
            def on_close(self):
                print("翻译器已断开")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    original_text = ""
                    translated_text = ""
                    
                    # 处理原文
                    if transcription_result and transcription_result.words:
                        for word in transcription_result.words:
                            if word.fixed:
                                original_text += word.text
                                
                    # 处理翻译
                    if translation_result:
                        translation = translation_result.get_translation(self.parent.target_language)
                        if translation and translation.words:
                            for word in translation.words:
                                if word.fixed:
                                    translated_text += word.text
                                    
                            # 如果句子结束，生成语音
                            if translation.is_sentence_end and translated_text.strip():
                                self._generate_speech(translated_text.strip())
                                
                    if original_text or translated_text:
                        self.parent.result_queue.put({
                            'original': original_text,
                            'translated': translated_text,
                            'timestamp': time.time()
                        })
                        
                except Exception as e:
                    print(f"翻译回调错误: {e}")
                    
            def _generate_speech(self, text):
                """生成语音"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self):
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                # 保存临时音频文件
                                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                temp_file.write(self.audio_data)
                                temp_file.close()
                                
                                # 添加到结果队列
                                self.parent.result_queue.put({
                                    'audio_file': temp_file.name,
                                    'timestamp': time.time()
                                })
                    
                    tts_callback = TTSCallback()
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=tts_callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS错误: {e}")
        
        try:
            callback = TranslateCallback(self)
            
            self.translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.target_language],
                callback=callback,
            )
            
            self.translator.start()
            
            while self.is_recording:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        self.translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频错误: {e}")
                    
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            
    def get_results(self):
        """获取翻译结果"""
        results = []
        while not self.result_queue.empty():
            try:
                result = self.result_queue.get_nowait()
                results.append(result)
            except queue.Empty:
                break
        return results


# 全局翻译器实例
translator = SimpleTranslator()

# 全局状态
current_original = ""
current_translated = ""
current_audio = None

def start_translation(language_pair):
    """开始翻译"""
    global current_original, current_translated, current_audio
    current_original = ""
    current_translated = ""
    current_audio = None
    
    status = translator.start_recording(language_pair)
    return status, "", "", None

def stop_translation():
    """停止翻译"""
    status = translator.stop_recording()
    return status

def update_results():
    """更新结果显示"""
    global current_original, current_translated, current_audio
    
    if not translator.is_recording:
        return current_original, current_translated, current_audio
        
    results = translator.get_results()
    
    for result in results:
        if 'original' in result and result['original']:
            current_original += result['original']
        if 'translated' in result and result['translated']:
            current_translated += result['translated']
        if 'audio_file' in result:
            current_audio = result['audio_file']
            
    return current_original, current_translated, current_audio

def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="实时语音翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型的实时翻译应用
        """)
        
        with gr.Row():
            language_choice = gr.Dropdown(
                choices=list(translator.languages.keys()),
                value="中文->英文",
                label="选择翻译语言",
                interactive=True
            )
            
        with gr.Row():
            start_btn = gr.Button("🎤 开始录音", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="原文",
                    lines=6,
                    interactive=False,
                    placeholder="原文将显示在这里..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="译文",
                    lines=6, 
                    interactive=False,
                    placeholder="翻译结果将显示在这里..."
                )
                
        audio_output = gr.Audio(
            label="🔊 翻译语音",
            interactive=False,
            autoplay=True
        )
        
        gr.Markdown("""
        ### 使用说明
        1. 选择翻译语言对
        2. 点击"开始录音"按钮
        3. 对着麦克风说话
        4. 系统会实时显示翻译结果并播放语音
        5. 点击"停止录音"结束翻译
        """)
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_choice],
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display]
        )
        
        # 定时更新结果
        app.load(
            fn=update_results,
            outputs=[original_output, translated_output, audio_output],
            every=1
        )
    
    return app

if __name__ == "__main__":
    # 检查API密钥
    if not dashscope.api_key or dashscope.api_key.startswith('<'):
        print("❌ 请先配置DASHSCOPE_API_KEY")
        print("在.env文件中设置: DASHSCOPE_API_KEY=your_key")
    else:
        print("🚀 启动简化版翻译应用...")
        app = create_interface()
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False
        )