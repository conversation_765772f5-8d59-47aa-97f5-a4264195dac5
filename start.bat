@echo off
chcp 65001 >nul
echo 🚀 启动实时翻译应用...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查是否已安装依赖
python -c "import dashscope, gradio, pyaudio" >nul 2>&1
if errorlevel 1 (
    echo 📦 检测到缺少依赖，开始自动安装...
    python install.py
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查API密钥配置
if not exist .env (
    echo ⚠️  未找到.env配置文件，正在创建...
    echo DASHSCOPE_API_KEY=your_api_key_here > .env
    echo.
    echo 📝 请编辑.env文件，设置您的DASHSCOPE_API_KEY
    echo 然后重新运行此脚本
    pause
    exit /b 1
)

REM 启动应用
echo ✅ 配置检查完成，启动应用...
python simple_translator.py

pause