"""
快速配置检查脚本
"""

import os
import dashscope

def check_config():
    print("🔍 检查配置...")
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✅ .env文件存在")
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'DASHSCOPE_API_KEY=' in content:
                if 'your_api_key_here' in content:
                    print("❌ API密钥未配置，请编辑.env文件")
                    return False
                else:
                    print("✅ API密钥已配置")
            else:
                print("❌ .env文件格式错误")
                return False
    else:
        print("❌ .env文件不存在")
        return False
    
    # 检查环境变量
    if 'DASHSCOPE_API_KEY' in os.environ:
        dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
    else:
        try:
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('DASHSCOPE_API_KEY='):
                        key = line.split('=')[1].strip()
                        if key and key != 'your_api_key_here':
                            dashscope.api_key = key
                            break
        except:
            pass
    
    if dashscope.api_key and not dashscope.api_key.startswith('<'):
        print(f"✅ API密钥加载成功: {dashscope.api_key[:8]}...")
        return True
    else:
        print("❌ API密钥加载失败")
        return False

if __name__ == "__main__":
    if check_config():
        print("\n🎉 配置检查通过！")
        print("您可以正常使用实时翻译应用了")
        print("\n📝 使用步骤:")
        print("1. 打开浏览器访问 http://localhost:7860")
        print("2. 选择翻译语言对")
        print("3. 点击'开始录音'按钮")
        print("4. 对着麦克风说话")
        print("5. 查看实时翻译结果")
    else:
        print("\n❌ 配置检查失败！")
        print("请按以下步骤修复:")
        print("1. 编辑.env文件")
        print("2. 将DASHSCOPE_API_KEY=your_api_key_here中的your_api_key_here替换为您的实际API密钥")
        print("3. 保存文件后重新启动应用")