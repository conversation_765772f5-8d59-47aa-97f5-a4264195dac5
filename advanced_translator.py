"""
高级实时双向翻译应用
解决语音回环问题，支持双向对话翻译
"""

import os
import time
import tempfile
import threading
import queue
import json
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class AdvancedTranslator:
    """高级双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        self.current_mode = "A->B"  # A->B 或 B->A
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
            "中文 ⇄ 法文": {"A": "zh", "B": "fr", "A_name": "中文", "B_name": "Français"},
            "中文 ⇄ 德文": {"A": "zh", "B": "de", "A_name": "中文", "B_name": "Deutsch"},
            "中文 ⇄ 西班牙文": {"A": "zh", "B": "es", "A_name": "中文", "B_name": "Español"},
        }
        
        # 当前语言对
        self.current_pair = None
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.translator = None
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 500  # 静音阈值
        self.min_speech_duration = 1.0  # 最小语音持续时间
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
    def init_api_key(self):
        """初始化API密钥"""
        # 优先从环境变量读取
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            # 从.env文件读取
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        # 保存到.env文件
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_translation(self, language_pair: str, mode: str):
        """开始双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, ""
            
        if self.is_translating:
            return "翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, ""
            
        # 设置语言对
        self.current_pair = self.language_pairs[language_pair]
        self.lang_A = self.current_pair["A"]
        self.lang_B = self.current_pair["B"] 
        self.lang_A_name = self.current_pair["A_name"]
        self.lang_B_name = self.current_pair["B_name"]
        self.current_mode = mode
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        
        # 启动翻译线程
        threading.Thread(target=self._translation_worker, daemon=True).start()
        
        source_lang = self.lang_A_name if mode == "A->B" else self.lang_B_name
        target_lang = self.lang_B_name if mode == "A->B" else self.lang_A_name
        
        return f"✅ 开始翻译: {source_lang} → {target_lang}", "", "", None, ""
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
        
    def switch_mode(self):
        """切换翻译方向"""
        if not self.is_translating:
            return "请先开始翻译", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
            
        # 切换模式
        self.current_mode = "B->A" if self.current_mode == "A->B" else "A->B"
        
        source_lang = self.lang_A_name if self.current_mode == "A->B" else self.lang_B_name
        target_lang = self.lang_B_name if self.current_mode == "A->B" else self.lang_A_name
        
        # 重启翻译器
        self._restart_translator()
        
        return f"🔄 已切换: {source_lang} → {target_lang}", "", "", None, self._get_conversation_display()
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        if self.translator:
            try:
                self.translator.stop()
            except:
                pass
            self.translator = None
            
    def _restart_translator(self):
        """重启翻译器"""
        self._cleanup_resources()
        time.sleep(0.5)  # 短暂等待
        threading.Thread(target=self._translation_worker, daemon=True).start()
        
    def _translation_worker(self):
        """翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动翻译识别
            self._start_translation_recognition()
            
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _record_audio(self):
        """录音线程 - 带防回环机制"""
        print("🎤 开始录音...")
        silence_count = 0
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
                # 计算音频能量，检测静音
                audio_data = np.frombuffer(data, dtype=np.int16)
                energy = np.sqrt(np.mean(audio_data**2))
                
                if energy > self.silence_threshold:
                    silence_count = 0
                    self.audio_queue.put(data)
                else:
                    silence_count += 1
                    # 静音时间过长，暂停发送
                    if silence_count < 10:  # 允许短暂静音
                        self.audio_queue.put(data)
                        
            except Exception as e:
                print(f"录音错误: {e}")
                break
                
    def _start_translation_recognition(self):
        """启动翻译识别"""
        class TranslationCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                
            def on_open(self):
                print("🌐 翻译器已连接")
                
            def on_close(self):
                print("🌐 翻译器已断开")
                
            def on_error(self, message):
                print(f"❌ 翻译错误: {message}")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    # 处理转录结果
                    if transcription_result and transcription_result.text:
                        self.parent.current_original = transcription_result.text
                        print(f"原文: {transcription_result.text}")
                        
                    # 处理翻译结果
                    if translation_result:
                        source_lang = self.parent.lang_A if self.parent.current_mode == "A->B" else self.parent.lang_B
                        target_lang = self.parent.lang_B if self.parent.current_mode == "A->B" else self.parent.lang_A
                        
                        translation = translation_result.get_translation(target_lang)
                        if translation and translation.text:
                            self.parent.current_translated = translation.text
                            print(f"译文: {translation.text}")
                            
                            # 句子结束时生成语音并记录对话
                            if translation.is_sentence_end:
                                self._add_to_conversation(
                                    self.parent.current_original,
                                    self.parent.current_translated,
                                    self.parent.current_mode
                                )
                                self._generate_and_play_tts(translation.text)
                                
                except Exception as e:
                    print(f"翻译事件处理错误: {e}")
                    
            def _add_to_conversation(self, original, translated, mode):
                """添加到对话历史"""
                source_lang = self.parent.lang_A_name if mode == "A->B" else self.parent.lang_B_name
                target_lang = self.parent.lang_B_name if mode == "A->B" else self.parent.lang_A_name
                
                self.parent.conversation_history.append({
                    'timestamp': time.strftime('%H:%M:%S'),
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'original': original,
                    'translated': translated
                })
                
            def _generate_and_play_tts(self, text):
                """生成并播放TTS - 带防回环机制"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                # 设置播放状态，防止回环
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = True
                                
                                try:
                                    # 保存音频文件
                                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                    temp_file.write(self.audio_data)
                                    temp_file.close()
                                    self.parent.current_audio = temp_file.name
                                    print(f"🔊 语音已生成: {temp_file.name}")
                                    
                                    # 播放音频后等待一段时间
                                    threading.Thread(target=self._reset_play_state, daemon=True).start()
                                    
                                except Exception as e:
                                    print(f"音频处理错误: {e}")
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = False
                                        
                        def _reset_play_state(self):
                            """重置播放状态"""
                            time.sleep(3)  # 等待音频播放完成
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                            print("🔊 音频播放完成，恢复录音")
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                    
                    callback = TTSCallback(self.parent)
                    
                    # 根据目标语言选择合适的声音
                    target_lang = self.parent.lang_B if self.parent.current_mode == "A->B" else self.parent.lang_A
                    voice = self._get_voice_for_language(target_lang)
                    
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice=voice,
                        callback=callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
                    with self.parent.audio_play_lock:
                        self.parent.is_playing_audio = False
                        
            def _get_voice_for_language(self, lang_code):
                """根据语言选择合适的声音"""
                voice_map = {
                    'zh': 'longhua_v2',
                    'en': 'longhua_v2', 
                    'ja': 'longhua_v2',
                    'ko': 'longhua_v2',
                    'fr': 'longhua_v2',
                    'de': 'longhua_v2',
                    'es': 'longhua_v2'
                }
                return voice_map.get(lang_code, 'longhua_v2')
        
        try:
            callback = TranslationCallback(self)
            
            # 确定源语言和目标语言
            source_lang = self.lang_A if self.current_mode == "A->B" else self.lang_B
            target_lang = self.lang_B if self.current_mode == "A->B" else self.lang_A
            
            self.translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[target_lang],
                callback=callback,
            )
            
            self.translator.start()
            print(f"🚀 翻译器已启动 - {source_lang} → {target_lang}")
            
            # 发送音频数据
            while self.is_translating:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        self.translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频错误: {e}")
                    break
                    
        except Exception as e:
            print(f"翻译识别启动失败: {e}")
            
    def _get_conversation_display(self):
        """获取对话历史显示"""
        if not self.conversation_history:
            return "对话历史将显示在这里..."
            
        display_text = ""
        for i, conv in enumerate(self.conversation_history[-10:], 1):  # 只显示最近10条
            display_text += f"[{conv['timestamp']}] {conv['source_lang']} → {conv['target_lang']}\n"
            display_text += f"原文: {conv['original']}\n"
            display_text += f"译文: {conv['translated']}\n"
            display_text += "-" * 50 + "\n"
            
        return display_text
        
    def get_current_results(self):
        """获取当前结果"""
        return self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()

# 全局实例
translator = AdvancedTranslator()

def set_api_key(api_key):
    """设置API密钥"""
    return translator.set_api_key(api_key)

def start_translation(language_pair, mode):
    """开始翻译"""
    return translator.start_translation(language_pair, mode)

def stop_translation():
    """停止翻译"""
    return translator.stop_translation()

def switch_mode():
    """切换翻译方向"""
    return translator.switch_mode()

def refresh_results():
    """刷新结果"""
    return translator.get_current_results()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="高级实时双向翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 高级实时双向翻译器
        
        **适用场景**: 面对面跨语言对话翻译
        
        **特色功能**: 
        - 🔄 双向实时翻译
        - 🎤 智能语音检测
        - 🔊 防回环播放
        - 📝 对话历史记录
        """)
        
        # API密钥设置区域
        with gr.Accordion("🔑 API密钥设置", open=not translator.api_key):
            gr.Markdown("""
            ### 获取API密钥
            1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
            2. 注册/登录阿里云账号
            3. 开通百炼服务
            4. 创建API密钥
            5. 将密钥粘贴到下方输入框
            """)
            
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    type="password",
                    value=translator.api_key if translator.api_key else ""
                )
                set_key_btn = gr.Button("💾 保存密钥", variant="primary")
                
            key_status = gr.Textbox(
                label="设置状态",
                value="✅ API密钥已配置" if translator.api_key else "⚠️ 请设置API密钥",
                interactive=False
            )
        
        # 翻译设置区域
        with gr.Row():
            language_pair = gr.Dropdown(
                choices=list(translator.language_pairs.keys()),
                value="中文 ⇄ 英文",
                label="选择语言对",
                interactive=True
            )
            
            translation_mode = gr.Radio(
                choices=["A->B", "B->A"],
                value="A->B",
                label="翻译方向",
                interactive=True
            )
        
        # 控制按钮
        with gr.Row():
            start_btn = gr.Button("🎤 开始对话翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️停止翻译", variant="secondary", size="lg")
            switch_btn = gr.Button("🔄 切换方向", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        # 实时翻译结果
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 当前原文",
                    lines=4,
                    interactive=False,
                    placeholder="原文将显示在这里..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 当前译文",
                    lines=4,
                    interactive=False,
                    placeholder="译文将显示在这里..."
                )
        
        # 语音播放
        audio_output = gr.Audio(
            label="🔊 翻译语音（自动播放）",
            interactive=False,
            autoplay=True
        )
        
        # 对话历史
        conversation_history = gr.Textbox(
            label="📝 对话历史",
            lines=10,
            interactive=False,
            placeholder="对话历史将显示在这里..."
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        
        #### 🔧 设置步骤
        1. **设置API密钥**: 点击上方"API密钥设置"区域，输入您的阿里云API密钥
        2. **选择语言对**: 选择需要翻译的语言对（如"中文 ⇄ 英文"）
        3. **选择方向**: 选择初始翻译方向（A->B 或 B->A）
        
        #### 🎤 对话流程
        1. **开始翻译**: 点击"开始对话翻译"按钮
        2. **说话**: 对着麦克风说话，系统会自动识别并翻译
        3. **听取**: 翻译结果会自动播放语音
        4. **切换**: 需要对方说话时，点击"切换方向"按钮
        5. **继续**: 重复上述过程进行双向对话
        6. **结束**: 点击"停止翻译"结束对话
        
        #### 🛡️ 防回环机制
        - 系统会在播放翻译语音时暂停录音，防止无限循环
        - 智能检测静音和语音，避免误触发
        - 自动过滤背景噪音和短暂声音
        
        #### 💡 使用技巧
        - 说话时保持清晰，适当停顿
        - 等待翻译语音播放完成后再继续说话
        - 定期点击"刷新结果"查看最新内容
        - 查看对话历史了解完整对话内容
        """)
        
        # 事件绑定
        set_key_btn.click(
            fn=set_api_key,
            inputs=[api_key_input],
            outputs=[key_status]
        )
        
        start_btn.click(
            fn=start_translation,
            inputs=[language_pair, translation_mode],
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history]
        )
        
        switch_btn.click(
            fn=switch_mode,
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history]
        )
        
        refresh_btn.click(
            fn=refresh_results,
            outputs=[original_output, translated_output, audio_output, conversation_history]
        )
    
    return app

if __name__ == "__main__":
    print("🚀 启动高级双向翻译应用...")
    
    config_ok, msg = translator.check_config()
    if config_ok:
        print("✅ 配置检查通过")
    else:
        print(f"⚠️ 配置问题: {msg}")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        show_error=True
    )