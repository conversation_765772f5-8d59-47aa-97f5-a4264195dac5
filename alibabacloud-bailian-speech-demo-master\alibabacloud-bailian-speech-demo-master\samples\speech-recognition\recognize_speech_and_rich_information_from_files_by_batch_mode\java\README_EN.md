[comment]: # (title and brief introduction of the sample)
## Batch Audio/Video File Rich Text Speech Recognition (Batch Mode)

English | [简体中文](./README.md)

## Java

[comment]: # (prerequisites)
### :point_right: Prerequisites

1. #### Configure Alibaba Cloud Bailian API-KEY

    Before running this example, you need to create an Alibaba Cloud account, obtain the Alibaba Cloud Bailian API-KEY, and complete necessary environment configurations. For detailed API-KEY configuration steps, please refer to: [PREREQUISITES.md](../../../../PREREQUISITES.md)

2. #### Java Runtime Environment

   Before running this example, you need to install Java runtime environment and Maven build tools.

[comment]: # (how to run the sample and expected results)
### :point_right: Run Example

You can run this example by executing run.sh (Linux/Mac systems) or run.bat (Windows systems).

When running the example, the audio file recognition service will process the submitted file list in the background. After successful transcription, the recognition results for each file will be parsed and printed in the terminal using the SenseVoiceParser tool.

[comment]: # (technical support of the sample)
### :point_right: Technical Support
<img src="https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/group-en.png" width="400"/>
