"""
工作版本的实时翻译应用
解决前端显示问题
"""

import os
import time
import tempfile
import threading
import queue
from typing import Optional, Tuple
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class WorkingTranslator:
    """工作版本的翻译器"""
    
    def __init__(self):
        self.init_api_key()
        self.is_recording = False
        self.audio_queue = queue.Queue()
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.languages = {
            "中文->英文": "en",
            "中文->日文": "ja", 
            "中文->韩文": "ko",
            "中文->法文": "fr",
            "中文->德文": "de",
            "中文->西班牙文": "es"
        }
        
        # 翻译器相关
        self.translator = None
        self.pyaudio_obj = None
        self.audio_stream = None
        self.target_language = "en"
        
        # 结果存储 - 使用简单的字符串变量
        self.original_text = ""
        self.translated_text = ""
        self.audio_file = None
        self.last_update_time = 0
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def check_config(self):
        """检查配置是否正确"""
        if not hasattr(dashscope, 'api_key') or not dashscope.api_key or dashscope.api_key == 'your_api_key_here':
            return False, "API密钥未配置"
        return True, "配置正确"
        
    def start_recording(self, language_pair: str):
        """开始录音和翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", self.original_text, self.translated_text, self.audio_file
            
        if self.is_recording:
            return "已在录音中...", self.original_text, self.translated_text, self.audio_file
            
        if language_pair not in self.languages:
            return "❌ 不支持的语言对", self.original_text, self.translated_text, self.audio_file
            
        # 重置状态
        self.is_recording = True
        self.target_language = self.languages[language_pair]
        self.original_text = ""
        self.translated_text = ""
        self.audio_file = None
        
        # 启动工作线程
        threading.Thread(target=self._work_thread, daemon=True).start()
        
        return f"✅ 开始录音 - {language_pair}", "", "", None
        
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return "未在录音中", self.original_text, self.translated_text, self.audio_file
            
        self.is_recording = False
        self._cleanup()
        return "✅ 录音已停止", self.original_text, self.translated_text, self.audio_file
        
    def _cleanup(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        if self.translator:
            try:
                self.translator.stop()
            except:
                pass
            self.translator = None
            
    def _work_thread(self):
        """主工作线程"""
        try:
            # 启动录音
            self._start_audio_recording()
            
            # 启动翻译
            self._start_translation()
            
        except Exception as e:
            print(f"工作线程错误: {e}")
            self.is_recording = False
            
    def _start_audio_recording(self):
        """启动音频录音"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录音启动失败: {e}")
            
    def _record_audio(self):
        """录音线程"""
        print("🎤 开始录音...")
        while self.is_recording:
            try:
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                self.audio_queue.put(data)
            except Exception as e:
                print(f"录音错误: {e}")
                break
                
    def _start_translation(self):
        """启动翻译"""
        class MyCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                
            def on_open(self):
                print("🌐 翻译器已连接")
                
            def on_close(self):
                print("🌐 翻译器已断开")
                
            def on_error(self, message):
                print(f"❌ 翻译错误: {message}")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    # 更新原文
                    if transcription_result and transcription_result.text:
                        self.parent.original_text = transcription_result.text
                        self.parent.last_update_time = time.time()
                        print(f"原文: {transcription_result.text}")
                        
                    # 更新翻译
                    if translation_result:
                        translation = translation_result.get_translation(self.parent.target_language)
                        if translation and translation.text:
                            self.parent.translated_text = translation.text
                            self.parent.last_update_time = time.time()
                            print(f"译文: {translation.text}")
                            
                            # 生成语音
                            if translation.is_sentence_end:
                                self._generate_tts(translation.text)
                                
                except Exception as e:
                    print(f"事件处理错误: {e}")
                    
            def _generate_tts(self, text):
                """生成TTS"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                temp_file.write(self.audio_data)
                                temp_file.close()
                                self.parent.audio_file = temp_file.name
                                self.parent.last_update_time = time.time()
                                print(f"🔊 语音已生成: {temp_file.name}")
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                    
                    callback = TTSCallback(self.parent)
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
        
        try:
            callback = MyCallback(self)
            
            self.translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.target_language],
                callback=callback,
            )
            
            self.translator.start()
            print(f"🚀 翻译器已启动")
            
            # 发送音频数据
            while self.is_recording:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        self.translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频错误: {e}")
                    break
                    
        except Exception as e:
            print(f"翻译启动失败: {e}")
            
    def get_current_results(self):
        """获取当前结果"""
        return self.original_text, self.translated_text, self.audio_file

# 全局实例
translator = WorkingTranslator()

def start_translation(language_pair):
    """开始翻译"""
    return translator.start_recording(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_recording()

def refresh_results():
    """刷新结果"""
    return translator.get_current_results()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="实时语音翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型的实时翻译应用
        
        **重要**: 点击"刷新结果"按钮查看最新的翻译结果
        """)
        
        # 配置检查
        config_ok, config_msg = translator.check_config()
        if config_ok:
            gr.Markdown("✅ **API配置正常**")
        else:
            gr.Markdown(f"❌ **配置问题**: {config_msg}")
        
        with gr.Row():
            language_choice = gr.Dropdown(
                choices=list(translator.languages.keys()),
                value="中文->英文",
                label="选择翻译语言对",
                interactive=True
            )
            
        with gr.Row():
            start_btn = gr.Button("🎤 开始录音翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary", size="lg")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 原文（语音识别）",
                    lines=8,
                    interactive=False,
                    placeholder="点击开始录音，然后说话..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 译文（实时翻译）",
                    lines=8, 
                    interactive=False,
                    placeholder="翻译结果将显示在这里..."
                )
                
        audio_output = gr.Audio(
            label="🔊 翻译语音",
            interactive=False
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        1. 选择翻译语言对
        2. 点击"开始录音翻译"
        3. 对着麦克风说话
        4. **点击"刷新结果"查看最新翻译**
        5. 点击"停止录音"结束
        
        ### 💡 提示
        - 需要定期点击"刷新结果"按钮查看最新内容
        - 说话时保持清晰，适当停顿
        - 确保麦克风权限已开启
        """)
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_choice],
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        refresh_btn.click(
            fn=refresh_results,
            outputs=[original_output, translated_output, audio_output]
        )
    
    return app

if __name__ == "__main__":
    print("🚀 启动工作版翻译应用...")
    
    config_ok, msg = translator.check_config()
    if config_ok:
        print("✅ 配置检查通过")
    else:
        print(f"⚠️ 配置问题: {msg}")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False,
        show_error=True
    )