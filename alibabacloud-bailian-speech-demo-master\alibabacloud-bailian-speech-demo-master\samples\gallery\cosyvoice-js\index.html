<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS JS</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            background-color: #fff;
            padding: 2rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            text-align: center;
            width: 80%;
            max-width: 800px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            margin-bottom: 1rem;
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 1rem;
            border-radius: 4px;
            background-color: #f9f9f9;
            width: 100%;
            margin-bottom: 1rem;
        }
        .input-container {
            display: flex;
            width: 100%;
        }
        #apikeyInput {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            align-self: flex-start;
        }
        #textInput {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            margin-right: 0.5rem;
        }
        #sendButton {
            background-color: #007bff;
            color: #fff;
            border: none;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            border-radius: 4px;
            cursor: pointer;
        }
        #sendButton:hover {
            background-color: #0056b3;
        }
        .message {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin-bottom: 0.5rem;
            max-width: 60%;
            word-wrap: break-word;
        }
        .user-message {
            align-self: flex-end;
            background-color: #007bff;
            color: #fff;
        }
        .response-message {
            align-self: flex-start;
            background-color: #ddd;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cosyvoice</h1>
        <div id="chat" class="chat-container"></div>
        <input type="text" id="apikeyInput" placeholder="your apikey">
        <div class="input-container">
            <input type="text" id="textInput" placeholder="Type your words here...">
            <button id="sendButton">Send</button>
        </div>
    </div>
    <script type="module">
        import PCMAudioPlayer from './audio_player.js';
        import Cosyvoice from './cosyvoice_api.js';

        document.addEventListener('DOMContentLoaded', function () {
            const sendButton = document.getElementById('sendButton');
            const apikeyInput = document.getElementById('apikeyInput');
            const textInput = document.getElementById('textInput');
            const chat = document.getElementById('chat');

            let activeResponseMessage = null;
            let player = new PCMAudioPlayer(22050);

            let cosyvoice = null;


            function addUserMessage(text) {
                addMessage(text, 'user-message');
            }

            function addMessage(text, className) {
                const message = document.createElement('div');
                message.className = `message ${className}`;
                message.textContent = text;
                chat.appendChild(message);
                chat.scrollTop = chat.scrollHeight;
                return message;
            }

            async function connectAndStreamAudio(apikey, text) {
                sendButton.disabled = true;
                cosyvoice = new Cosyvoice('wss://dashscope.aliyuncs.com/api-ws/v1/inference/?api_key='+apikey);
                await cosyvoice.connect((pcmData) => {
                    player.pushPCM(pcmData);
                });
                console.log('cosyvoice connected');
                console.log('cosyvoice send text', text);
                cosyvoice.sendText(text);
                await cosyvoice.stop();
                console.log('cosyvoice stopped');
                cosyvoice.close();
                sendButton.disabled = false;
            }

            sendButton.addEventListener('click', async () => {
                let apikey = apikeyInput.value.trim();
                if (!apikey) {
                    console.log('no apikey')
                    alert('请输入apikey');
                    return;
                }

                const text = textInput.value.trim();
                if(!text) {
                    console.log('no text')
                    alert('请输入文本');
                    return;
                }
                player.connect()
                player.stop()
                addUserMessage(text);
                textInput.value = '';
                await connectAndStreamAudio(apikey, text);
            });

            textInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendButton.click();
                }
            });
        });
    </script>
</body>
</html>
