# 🌍 实时语音翻译应用

基于阿里云百炼语音大模型的实时翻译应用，支持ASR（语音识别）+ 翻译 + TTS（语音合成）全流程。

## ✨ 功能特点

- 🎤 **实时语音识别**: 基于阿里云Paraformer模型
- 🌐 **多语言翻译**: 支持中英日韩法德西等多种语言互译
- 🔊 **语音合成**: 基于CosyVoice模型，自然流畅的语音输出
- 📱 **Web界面**: 基于Gradio的现代化Web界面
- ⚡ **流式处理**: 实时流式处理，低延迟体验

## 🎯 适用场景

- 🧳 **国外旅游**: 与当地人实时对话翻译
- 💼 **商务会议**: 跨语言商务交流
- 📚 **语言学习**: 练习口语和听力
- 🤝 **国际交流**: 各种跨语言沟通场景

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装Python 3.8+和以下依赖：

```bash
# 克隆项目
git clone <项目地址>
cd RealtimeTranslate-Ali

# 安装依赖
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 配置API密钥

1. 注册阿里云账号并开通百炼服务
2. 获取DASHSCOPE_API_KEY
3. 编辑`.env`文件，设置您的API密钥：

```bash
DASHSCOPE_API_KEY=your_actual_api_key_here
```

### 3. 启动应用

```bash
# 方式1: 使用启动脚本（推荐）
python run_app.py

# 方式2: 直接运行主程序
python realtime_translator_app.py
```

### 4. 使用应用

1. 打开浏览器访问 `http://localhost:7860`
2. 选择翻译语言对（如"中文->英文"）
3. 点击"开始录音翻译"按钮
4. 开始说话，系统会实时显示原文、译文并播放翻译语音
5. 点击"停止录音"结束翻译

## 🛠️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   音频输入       │    │   语音识别       │    │   语言翻译       │
│  (麦克风录音)    │───▶│  (Paraformer)   │───▶│   (Gummy)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐             │
│   音频播放       │    │   语音合成       │             │
│  (扬声器输出)    │◀───│  (CosyVoice)    │◀────────────┘
└─────────────────┘    └─────────────────┘
```

## 📋 支持的语言对

- 中文 ↔ 英文
- 中文 → 日文
- 中文 → 韩文  
- 中文 → 法文
- 中文 → 德文
- 中文 → 西班牙文

## ⚙️ 配置说明

### 音频参数
- 采样率: 16kHz
- 声道: 单声道
- 格式: PCM 16bit
- 缓冲区: 3200字节

### 模型配置
- ASR模型: `gummy-realtime-v1`
- TTS模型: `cosyvoice-v2`
- 语音: `longhua_v2`

## 🔧 故障排除

### 常见问题

1. **麦克风权限问题**
   - 确保浏览器已授权麦克风访问权限
   - 检查系统麦克风设置

2. **API密钥错误**
   - 检查`.env`文件中的API密钥是否正确
   - 确认阿里云账号已开通百炼服务

3. **依赖安装失败**
   - 使用国内镜像源安装: `pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`
   - Windows用户可能需要安装Visual Studio Build Tools

4. **音频播放问题**
   - 检查系统音频设备是否正常
   - 确认浏览器音频播放权限

### 系统要求

- **操作系统**: Windows 10+, macOS 10.14+, Linux
- **Python**: 3.8+
- **内存**: 建议4GB+
- **网络**: 稳定的互联网连接

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 技术支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 查看阿里云百炼官方文档