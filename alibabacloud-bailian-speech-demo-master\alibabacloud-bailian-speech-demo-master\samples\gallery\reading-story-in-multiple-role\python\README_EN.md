## Role-Based Story Reading

English | [简体中文](./README.md)

Role-based story reading utilizes different voice synthesis tones to narrate a complete story. It is suitable for various scenarios such as audiobooks and online education where speaker differentiation is required.
<!--
### :point_right: Applicable Scenarios

| Application Scenario | Typical Usage | Usage Instructions                |
|--------------|--------|---------------------|
 -->


## Python

### :point_right: Prerequisites

1. #### Configure Alibaba Cloud API-KEY

    Before running this example, you need to activate an Alibaba Cloud account, obtain the API-KEY, and perform necessary environment configurations. For detailed API-KEY configuration steps, please refer to: [PREREQUISITES.md](../../../../PREREQUISITES.md)

1. #### Install Python Dependencies

    The Alibaba Cloud SDK requires Python 3.8 or higher. You can install the dependencies for this example using the following command:
    ```commandline
    pip3 install -r requirements.txt
    ```

### :point_right: Run Example

```commandline
python3 run.py
```

After running the script, the story of little ducks will be narrated using different voices for three roles: "<PERSON> Mother," "Duck Baby," and "Narrator." The story content is stored in story.json.

[comment]: # (technical support of the sample)
### :point_right: Technical Support
<img src="https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/group-en.png" width="400"/>