"""
基于阿里云百炼语音大模型的实时翻译应用
功能：ASR（语音识别）+ 翻译 + TTS（语音合成）
适用场景：国外旅游、商务对话实时翻译
"""

import os
import queue
import threading
import time
import tempfile
import base64
from typing import Optional, Tuple, List
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class RealtimeTranslator:
    """实时翻译器核心类"""
    
    def __init__(self):
        self.init_dashscope_api_key()
        self.audio_queue = queue.Queue()
        self.translation_queue = queue.Queue()
        self.tts_queue = queue.Queue()
        self.is_recording = False
        self.current_session = None
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 支持的语言配置
        self.language_configs = {
            "中文->英文": {"source": "zh", "target": "en", "tts_voice": "longhua_v2"},
            "英文->中文": {"source": "en", "target": "zh", "tts_voice": "longhua_v2"},
            "中文->日文": {"source": "zh", "target": "ja", "tts_voice": "longhua_v2"},
            "中文->韩文": {"source": "zh", "target": "ko", "tts_voice": "longhua_v2"},
            "中文->法文": {"source": "zh", "target": "fr", "tts_voice": "longhua_v2"},
            "中文->德文": {"source": "zh", "target": "de", "tts_voice": "longhua_v2"},
            "中文->西班牙文": {"source": "zh", "target": "es", "tts_voice": "longhua_v2"},
        }
        
        self.pyaudio_instance = None
        self.audio_stream = None
        
    def init_dashscope_api_key(self):
        """初始化DashScope API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            # 如果环境变量中没有，尝试从本地配置文件读取
            try:
                with open('.env', 'r') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            dashscope.api_key = line.split('=')[1].strip()
                            break
            except FileNotFoundError:
                print("请设置DASHSCOPE_API_KEY环境变量或在.env文件中配置")
                
    def start_recording(self, language_pair: str) -> Tuple[str, str]:
        """开始录音和实时翻译"""
        if self.is_recording:
            return "已在录音中...", ""
            
        if language_pair not in self.language_configs:
            return "不支持的语言对", ""
            
        self.is_recording = True
        self.current_language_config = self.language_configs[language_pair]
        
        # 启动录音线程
        self.recording_thread = threading.Thread(target=self._recording_worker)
        self.recording_thread.daemon = True
        self.recording_thread.start()
        
        # 启动翻译线程
        self.translation_thread = threading.Thread(target=self._translation_worker)
        self.translation_thread.daemon = True
        self.translation_thread.start()
        
        # 启动TTS线程
        self.tts_thread = threading.Thread(target=self._tts_worker)
        self.tts_thread.daemon = True
        self.tts_thread.start()
        
        return f"开始录音 - {language_pair}", "正在监听中..."
        
    def stop_recording(self) -> Tuple[str, str]:
        """停止录音"""
        if not self.is_recording:
            return "未在录音中", ""
            
        self.is_recording = False
        
        # 清理音频流
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
            self.audio_stream = None
            
        if self.pyaudio_instance:
            self.pyaudio_instance.terminate()
            self.pyaudio_instance = None
            
        return "录音已停止", ""
        
    def _recording_worker(self):
        """录音工作线程"""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_instance.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            print("开始录音...")
            while self.is_recording:
                try:
                    data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_queue.put(data)
                except Exception as e:
                    print(f"录音错误: {e}")
                    break
                    
        except Exception as e:
            print(f"录音初始化错误: {e}")
            
    def _translation_worker(self):
        """翻译工作线程"""
        class TranslationCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.sentence_buffer = ""
                
            def on_open(self):
                print("翻译识别器已打开")
                
            def on_close(self):
                print("翻译识别器已关闭")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    if translation_result:
                        target_lang = self.parent.current_language_config["target"]
                        translation = translation_result.get_translation(target_lang)
                        
                        if translation and translation.words:
                            # 收集固定的翻译词汇
                            fixed_text = ""
                            for word in translation.words:
                                if word.fixed:
                                    fixed_text += word.text
                                    
                            if fixed_text.strip():
                                self.parent.translation_queue.put(("translation", fixed_text))
                                
                            # 如果句子结束，发送到TTS队列
                            if translation.is_sentence_end and fixed_text.strip():
                                self.parent.tts_queue.put(fixed_text.strip())
                                
                    if transcription_result and transcription_result.words:
                        # 收集原文
                        original_text = ""
                        for word in transcription_result.words:
                            if word.fixed:
                                original_text += word.text
                                
                        if original_text.strip():
                            self.parent.translation_queue.put(("original", original_text))
                            
                except Exception as e:
                    print(f"翻译回调错误: {e}")
        
        try:
            callback = TranslationCallback(self)
            target_lang = self.current_language_config["target"]
            
            translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[target_lang],
                callback=callback,
            )
            
            translator.start()
            print(f"翻译器已启动，请求ID: {translator.get_last_request_id()}")
            
            while self.is_recording:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频数据错误: {e}")
                    
            translator.stop()
            print("翻译器已停止")
            
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            
    def _tts_worker(self):
        """TTS工作线程"""
        class TTSCallback(ResultCallback):
            def __init__(self, parent):
                self.parent = parent
                self.audio_data = b""
                
            def on_open(self):
                print("TTS连接已打开")
                
            def on_complete(self):
                print("TTS合成完成")
                if self.audio_data:
                    # 保存音频文件并添加到播放队列
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                    temp_file.write(self.audio_data)
                    temp_file.close()
                    self.parent.translation_queue.put(("audio", temp_file.name))
                    self.audio_data = b""
                
            def on_error(self, message):
                print(f"TTS错误: {message}")
                
            def on_close(self):
                print("TTS连接已关闭")
                
            def on_data(self, data: bytes):
                self.audio_data += data
                
        while self.is_recording:
            try:
                if not self.tts_queue.empty():
                    text = self.tts_queue.get()
                    if text.strip():
                        callback = TTSCallback(self)
                        voice = self.current_language_config["tts_voice"]
                        
                        synthesizer = SpeechSynthesizer(
                            model='cosyvoice-v2',
                            voice=voice,
                            callback=callback
                        )
                        
                        synthesizer.call(text)
                        
                else:
                    time.sleep(0.1)
                    
            except Exception as e:
                print(f"TTS工作线程错误: {e}")
                
    def get_latest_results(self) -> Tuple[str, str, Optional[str]]:
        """获取最新的翻译结果"""
        original_text = ""
        translated_text = ""
        audio_file = None
        
        while not self.translation_queue.empty():
            try:
                result_type, content = self.translation_queue.get_nowait()
                if result_type == "original":
                    original_text += content
                elif result_type == "translation":
                    translated_text += content
                elif result_type == "audio":
                    audio_file = content
            except queue.Empty:
                break
                
        return original_text, translated_text, audio_file


# 全局翻译器实例
translator = RealtimeTranslator()

def start_translation(language_pair):
    """开始翻译"""
    status, message = translator.start_recording(language_pair)
    return status, message, "", None

def stop_translation():
    """停止翻译"""
    status, message = translator.stop_recording()
    return status, message

def update_translation_display():
    """更新翻译显示"""
    if translator.is_recording:
        original, translated, audio_file = translator.get_latest_results()
        return original, translated, audio_file
    return "", "", None

def create_gradio_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="实时语音翻译器", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型，支持实时语音识别、翻译和语音合成
        
        **适用场景：** 国外旅游、商务对话、跨语言交流
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 🎯 控制面板")
                
                language_dropdown = gr.Dropdown(
                    choices=list(translator.language_configs.keys()),
                    value="中文->英文",
                    label="选择翻译语言对",
                    interactive=True
                )
                
                with gr.Row():
                    start_btn = gr.Button("🎤 开始录音翻译", variant="primary")
                    stop_btn = gr.Button("⏹️ 停止录音", variant="secondary")
                
                status_text = gr.Textbox(
                    label="状态",
                    value="准备就绪",
                    interactive=False
                )
                
            with gr.Column(scale=2):
                gr.Markdown("### 📝 翻译结果")
                
                with gr.Row():
                    original_text = gr.Textbox(
                        label="原文",
                        lines=8,
                        interactive=False,
                        placeholder="原文将在这里显示..."
                    )
                    
                    translated_text = gr.Textbox(
                        label="译文", 
                        lines=8,
                        interactive=False,
                        placeholder="翻译结果将在这里显示..."
                    )
                
                audio_output = gr.Audio(
                    label="🔊 翻译语音",
                    interactive=False,
                    autoplay=True
                )
        
        gr.Markdown("""
        ### 📋 使用说明
        
        1. **选择语言对：** 从下拉菜单中选择源语言和目标语言
        2. **开始录音：** 点击"开始录音翻译"按钮，开始说话
        3. **实时翻译：** 系统会实时显示原文、译文，并播放翻译语音
        4. **停止录音：** 点击"停止录音"按钮结束翻译
        
        ### ⚙️ 环境配置
        
        请确保已设置 `DASHSCOPE_API_KEY` 环境变量，或在项目根目录创建 `.env` 文件：
        ```
        DASHSCOPE_API_KEY=your_api_key_here
        ```
        """)
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_dropdown],
            outputs=[status_text, gr.Textbox(visible=False), original_text, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_text, gr.Textbox(visible=False)]
        )
        
        # 定时更新翻译结果
        app.load(
            fn=update_translation_display,
            outputs=[original_text, translated_text, audio_output],
            every=0.5
        )
    
    return app

if __name__ == "__main__":
    # 检查API密钥
    if not dashscope.api_key or dashscope.api_key == '<your-dashscope-api-key>':
        print("⚠️  请先配置DASHSCOPE_API_KEY")
        print("方法1: 设置环境变量 DASHSCOPE_API_KEY=your_key")
        print("方法2: 创建.env文件，内容为 DASHSCOPE_API_KEY=your_key")
    else:
        print("🚀 启动实时翻译应用...")
        app = create_gradio_interface()
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=True
        )