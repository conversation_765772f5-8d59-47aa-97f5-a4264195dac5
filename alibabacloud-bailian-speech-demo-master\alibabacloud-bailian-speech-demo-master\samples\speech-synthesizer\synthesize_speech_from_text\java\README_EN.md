## Speech Synthesis and Save File (Simple Mode)

English | [简体中文](./README.md)

## Java

### :point_right: Prerequisites

1. #### Configure Alibaba Cloud API-KEY

    Before running this example, you need to create an Alibaba Cloud account, obtain the API-KEY, and complete environment configuration. For detailed API-KEY configuration steps, please refer to: [PREREQUISITES.md](../../../../PREREQUISITES.md)

2. #### Java Runtime Environment

   Before running this example, you need to install Java runtime environment and Maven build tool.

### :point_right: How to Run the Example

You can run this example by executing run.sh (for Linux/Mac systems) or run.bat (for Windows systems).

When running the example, it will synthesize the sample text "想不到时间过得这么快！昨天和你视频聊天，看到你那自豪又满意的笑容，我的心里呀，就如同喝了一瓶蜜一样甜呢！真心为你开心呢！" using the longhua_v2 voice style and save it to the `result.mp3` file. You can modify `textToSynthesize` to specify the text for synthesis.

### :point_right: Technical Support
<img src="https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/group-en.png" width="400"/>
