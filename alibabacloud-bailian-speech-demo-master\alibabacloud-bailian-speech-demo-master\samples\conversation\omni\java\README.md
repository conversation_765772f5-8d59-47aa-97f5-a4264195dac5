# Omni-Realtime全模态大模型示例

简体中文 | [English](./README_EN.md)

本示例演示如何使用omni-realtime全模态大模型API，包括支持打断的音频交互、视频交互。

## 前提条件

#### 配置阿里云百炼API-KEY
在使用百炼SDK进行模型调用之前，您需要先在阿里云控制台创建模型服务并获取API-KEY。
- 在[百炼控制台](https://bailian.console.aliyun.com/)界面左下角找到API-KEY入口，点击后进入管理页面。
- 点击【创建新的API-KEY】，会自动创建一条属于这个账号的API-KEY。列表上展示API-KEY密文，点击【查看】可以看到API-KEY的明文信息。请注意保存API-KEY的明文信息，后续使用API-KEY时需要用到。
- 更多百炼配置信息请参考：[PREREQUISITES.md](../../../../PREREQUISITES.md)

### 一、语音交互示例
语音交互示例在运行后将会通过麦克风录制您的语音输入，通过扬声器播放omni模型返回的音频。您可以随时讲话打断大模型的输出。为了避免播放模型输出时自打断，推荐您戴耳机体验。
```shell
cd run_server_vad
./run.sh
```


### 二、视频交互示例
视频交互示例在运行后将会通过麦克风录制您的语音输入，通过摄像头采集视频输入。通过扬声器播放omni模型返回的音频。

您可以随时讲话打断大模型的输出。为了避免播放模型输出时自打断，推荐您戴耳机体验。
1. 运行服务
```shell
cd run_with_camera
./run.sh
```
此时还没有开始视频采集，会保持语音交互。

2. 通过网页前端打开摄像头
在开启交互后，双击打开`interface/index.html`页面，并且打开摄像头权限。点击`创建连接`按钮将视频流发送给python程序，开始传输视频。

    您可以在开始语音交互后的任意时刻打开或者关闭网页中的连接，开始或结束传输视频。

### 三、非server_vad模式示例

本示例会模拟不使用`server_vad`模式，主动控制模型回复的时机。运行示例将会模拟多轮对话，将大模型生成的音频通过扬声器播放，等待上一次的回复播放完成后开始下一次任务。

```shell
cd run_without_server_vad
./run.sh
```


### 关于播放器的说明

在`b64_pcm_player.py`中，我们使用 pyaudio 实现了支持打断的流式播放器。播放器的输入是base 64编码的pcm格式音频。

在播放器中有两个工作线程，一个线程负责从输入读取base64编码数据并完成解码，另一个线程负责将音频按照`chunk_size`分片段进行播放。音频片段的播放是阻塞的，因此`chunk_size`过大会影响打断的延迟，推荐配置为100ms。
