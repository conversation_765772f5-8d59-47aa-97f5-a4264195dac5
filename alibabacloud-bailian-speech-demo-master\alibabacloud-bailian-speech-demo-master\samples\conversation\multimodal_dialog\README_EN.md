# DashScope Multi-modal Dialog API Integration Guide
English | [简体中文](./README.md)

## Overview

The DashScope Multimodal Dialog API provides powerful multimodal conversation capabilities. The multimodal dialog service offers Speech-to-Speech real-time interaction and supports multimodal data dialogue (e.g., images and videos). Additionally, the service supports the use of plugins, official agents, and third-party agents within the Bailian ecosystem.

## Features

- 🎤 **Voice Conversation**: Real-time speech recognition and synthesis 
- 🖼️ **Image Q&A**: Intelligent Q&A based on image content
- 💬 **Multiple Interaction Modes**: Push2Talk, Tap2Talk, Duplex
- 🔄 **Streaming Processing**: Real-time audio stream transmission and processing
- 🌐 **WebSocket Connection**: Low-latency real-time communication
- 🧩 **Agent Support**: Bailian official and third-party Agent integration

## Prerequisites

### 1. Obtain API Credentials
Please log in to the multimodal dialog console, activate the service, and create an application.then you can get those params:
- **API Key**: For authentication
- **Workspace ID**: Workspace identifier
- **App ID**: Application identifier

### 2. Environment Requirements

#### Java Environment
- Java 8 or higher
- Maven 3.6+

#### Python Environment
- Python 3.9 or higher
- pip package manager

### Others
For more API details, please refer to the official documentation [python](https://help.aliyun.com/zh/model-studio/multimodal-sdk-python) and [Java](https://help.aliyun.com/zh/model-studio/multimodal-sdk-java).

