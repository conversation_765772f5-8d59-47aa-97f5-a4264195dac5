"""
实时翻译应用启动脚本
简化版本，更容易部署和使用
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['gradio', 'dashscope', 'pyaudio', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_api_key():
    """检查API密钥配置"""
    if 'DASHSCOPE_API_KEY' in os.environ:
        return True
    
    # 检查.env文件
    if os.path.exists('.env'):
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('DASHSCOPE_API_KEY='):
                    key = line.split('=')[1].strip()
                    if key and key != 'your_api_key_here':
                        os.environ['DASHSCOPE_API_KEY'] = key
                        return True
    
    return False

def create_env_template():
    """创建环境变量模板文件"""
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write('# 阿里云百炼API密钥\n')
            f.write('# 请将your_api_key_here替换为您的实际API密钥\n')
            f.write('DASHSCOPE_API_KEY=your_api_key_here\n')
        print("✅ 已创建.env配置文件模板")

def main():
    print("🚀 启动实时翻译应用...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建配置文件模板
    create_env_template()
    
    # 检查API密钥
    if not check_api_key():
        print("❌ 未找到DASHSCOPE_API_KEY配置")
        print("请按以下步骤配置API密钥:")
        print("1. 编辑.env文件")
        print("2. 将DASHSCOPE_API_KEY=your_api_key_here中的your_api_key_here替换为您的实际API密钥")
        print("3. 保存文件后重新运行此脚本")
        sys.exit(1)
    
    print("✅ 配置检查完成")
    print("🌐 启动Web界面...")
    
    # 启动应用
    try:
        from realtime_translator_app import create_gradio_interface
        app = create_gradio_interface()
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()