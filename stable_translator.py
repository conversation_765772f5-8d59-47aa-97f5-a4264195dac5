"""
稳定版实时翻译应用
专注解决音频输入问题
"""

import os
import time
import tempfile
import threading
import queue
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class StableTranslator:
    """稳定版翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        
        # 音频参数 - 优化设置
        self.sample_rate = 16000
        self.chunk_size = 1600  # 减小chunk大小
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 → 英文": {"source": "zh", "target": "en", "source_name": "中文", "target_name": "English"},
            "英文 → 中文": {"source": "en", "target": "zh", "source_name": "English", "target_name": "中文"},
            "中文 → 日文": {"source": "zh", "target": "ja", "source_name": "中文", "target_name": "日本語"},
            "日文 → 中文": {"source": "ja", "target": "zh", "source_name": "日本語", "target_name": "中文"},
        }
        
        # 当前语言设置
        self.source_lang = "zh"
        self.target_lang = "en"
        self.source_name = "中文"
        self.target_name = "English"
        
        # 音频处理
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer = None
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
        # 音频缓冲
        self.audio_buffer = []
        self.buffer_size = 10  # 缓冲10个chunk
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_translation(self, language_pair: str):
        """开始翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, ""
            
        if self.is_translating:
            return "翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, ""
            
        # 设置语言对
        current_pair = self.language_pairs[language_pair]
        self.source_lang = current_pair["source"]
        self.target_lang = current_pair["target"]
        self.source_name = current_pair["source_name"]
        self.target_name = current_pair["target_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.audio_buffer = []
        
        # 启动翻译线程
        threading.Thread(target=self._translation_worker, daemon=True).start()
        
        return f"✅ 开始翻译: {self.source_name} → {self.target_name}", "", "", None, ""
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        if self.recognizer:
            try:
                self.recognizer.stop()
            except:
                pass
            self.recognizer = None
            
    def _translation_worker(self):
        """翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动识别器
            self._start_recognition()
            
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            
            # 检查音频设备
            print("🎤 检查音频设备...")
            for i in range(self.pyaudio_obj.get_device_count()):
                info = self.pyaudio_obj.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    print(f"输入设备 {i}: {info['name']}")
            
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                input_device_index=None  # 使用默认设备
            )
            
            print("🎤 音频流已启动")
            
            # 启动录音线程
            threading.Thread(target=self._record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _record_audio(self):
        """录音线程"""
        print("🎤 开始录音...")
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                # 读取音频数据
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
                # 检查音频质量
                audio_data = np.frombuffer(data, dtype=np.int16)
                if len(audio_data) > 0:
                    # 计算音频能量
                    energy = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
                    
                    # 只有足够的能量才发送
                    if energy > 100:  # 降低阈值
                        self.audio_buffer.append(data)
                        
                        # 当缓冲区满时发送
                        if len(self.audio_buffer) >= self.buffer_size:
                            combined_data = b''.join(self.audio_buffer)
                            self._send_to_recognizer(combined_data)
                            self.audio_buffer = []
                    
            except Exception as e:
                print(f"录音错误: {e}")
                break
                
    def _send_to_recognizer(self, audio_data):
        """发送音频数据到识别器"""
        try:
            if self.recognizer and len(audio_data) > 0:
                self.recognizer.send_audio_frame(audio_data)
                print(f"📤 发送音频数据: {len(audio_data)} 字节")
        except Exception as e:
            print(f"发送音频到识别器错误: {e}")
            
    def _start_recognition(self):
        """启动识别器"""
        try:
            class RecognizerCallback(TranslationRecognizerCallback):
                def __init__(self, parent):
                    super().__init__()
                    self.parent = parent
                    
                def on_open(self):
                    print(f"🌐 {self.parent.source_name}识别器已连接")
                    
                def on_close(self):
                    print(f"🌐 {self.parent.source_name}识别器已断开")
                    
                def on_error(self, message):
                    print(f"❌ 识别错误: {message}")
                    
                def on_event(self, request_id, transcription_result, translation_result, usage):
                    try:
                        if transcription_result and transcription_result.text:
                            self.parent.current_original = transcription_result.text
                            print(f"[识别] 原文: {transcription_result.text}")
                            
                            # 处理翻译结果
                            if translation_result:
                                translation = translation_result.get_translation(self.parent.target_lang)
                                if translation and translation.text:
                                    self.parent.current_translated = translation.text
                                    print(f"[翻译] 译文: {translation.text}")
                                    
                                    if translation.is_sentence_end:
                                        self._handle_translation_complete(
                                            transcription_result.text,
                                            translation.text
                                        )
                                        
                    except Exception as e:
                        print(f"识别事件处理错误: {e}")
                        
                def _handle_translation_complete(self, original, translated):
                    """处理翻译完成"""
                    # 添加到对话历史
                    self.parent.conversation_history.append({
                        'timestamp': time.strftime('%H:%M:%S'),
                        'source_lang': self.parent.source_name,
                        'target_lang': self.parent.target_name,
                        'original': original,
                        'translated': translated
                    })
                    
                    # 生成语音
                    self._generate_and_play_tts(translated)
                    
                def _generate_and_play_tts(self, text):
                    """生成并播放TTS"""
                    try:
                        class TTSCallback(ResultCallback):
                            def __init__(self, parent):
                                self.parent = parent
                                self.audio_data = b""
                                
                            def on_data(self, data: bytes):
                                self.audio_data += data
                                
                            def on_complete(self):
                                if self.audio_data:
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = True
                                    
                                    try:
                                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                        temp_file.write(self.audio_data)
                                        temp_file.close()
                                        self.parent.current_audio = temp_file.name
                                        print(f"🔊 语音已生成")
                                        
                                        # 延迟恢复录音
                                        threading.Thread(target=self._reset_play_state, daemon=True).start()
                                        
                                    except Exception as e:
                                        print(f"音频处理错误: {e}")
                                        with self.parent.audio_play_lock:
                                            self.parent.is_playing_audio = False
                                            
                            def _reset_play_state(self):
                                """重置播放状态"""
                                time.sleep(3)  # 等待播放完成
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = False
                                print("🔊 音频播放完成，恢复录音")
                                    
                            def on_error(self, message):
                                print(f"TTS错误: {message}")
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = False
                        
                        callback = TTSCallback(self.parent)
                        
                        synthesizer = SpeechSynthesizer(
                            model='cosyvoice-v2',
                            voice='longhua_v2',
                            callback=callback
                        )
                        synthesizer.call(text)
                        
                    except Exception as e:
                        print(f"TTS生成失败: {e}")
                        with self.parent.audio_play_lock:
                            self.parent.is_playing_audio = False
            
            callback = RecognizerCallback(self)
            
            self.recognizer = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.target_lang],
                callback=callback,
            )
            
            self.recognizer.start()
            print(f"🚀 {self.source_name}识别器已启动")
            
        except Exception as e:
            print(f"识别器启动失败: {e}")
            
    def _get_conversation_display(self):
        """获取对话历史显示"""
        if not self.conversation_history:
            return "📝 对话历史将显示在这里..."
            
        display_text = "📝 对话记录:\n" + "="*50 + "\n\n"
        
        for i, conv in enumerate(self.conversation_history[-10:], 1):
            display_text += f"[{conv['timestamp']}] {conv['source_lang']} → {conv['target_lang']}\n"
            display_text += f"🎤 原文: {conv['original']}\n"
            display_text += f"🌐 译文: {conv['translated']}\n"
            display_text += "-" * 40 + "\n\n"
            
        return display_text
        
    def get_current_results(self):
        """获取当前结果"""
        return self.current_original, self.current_translated, self.current_audio, self._get_conversation_display()

# 全局实例
translator = StableTranslator()

def set_api_key(api_key):
    """设置API密钥"""
    return translator.set_api_key(api_key)

def start_translation(language_pair):
    """开始翻译"""
    return translator.start_translation(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_translation()

def refresh_results():
    """刷新结果"""
    return translator.get_current_results()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="稳定版实时翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🎯 稳定版实时翻译器
        
        **🔧 专注稳定性**: 解决音频输入问题，确保可靠运行
        
        **✨ 核心功能**: 
        - 🎤 稳定语音捕获
        - 🌐 实时翻译
        - 🔊 语音合成
        - 📝 对话记录
        
        **🎯 使用说明**: 选择翻译方向，开始对话
        """)
        
        # API密钥设置区域
        with gr.Accordion("🔑 API密钥设置", open=not translator.api_key):
            gr.Markdown("""
            ### 🔗 获取API密钥
            1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
            2. 注册/登录阿里云账号
            3. 开通百炼服务（有免费额度）
            4. 创建API密钥
            5. 将密钥粘贴到下方输入框
            """)
            
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    type="password",
                    value=translator.api_key if translator.api_key else ""
                )
                set_key_btn = gr.Button("💾 保存密钥", variant="primary")
                
            key_status = gr.Textbox(
                label="设置状态",
                value="✅ API密钥已配置" if translator.api_key else "⚠️ 请设置API密钥",
                interactive=False
            )
        
        # 翻译设置区域
        with gr.Row():
            language_pair = gr.Dropdown(
                choices=list(translator.language_pairs.keys()),
                value="中文 → 英文",
                label="选择翻译方向",
                interactive=True
            )
        
        # 控制按钮
        with gr.Row():
            start_btn = gr.Button("🎤 开始翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止翻译", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        # 实时翻译结果
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 原文",
                    lines=4,
                    interactive=False,
                    placeholder="说话时原文将显示在这里..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 译文",
                    lines=4,
                    interactive=False,
                    placeholder="翻译结果将显示在这里..."
                )
        
        # 语音播放
        audio_output = gr.Audio(
            label="🔊 翻译语音",
            interactive=False,
            autoplay=True
        )
        
        # 对话历史
        conversation_history = gr.Textbox(
            label="📝 对话历史",
            lines=12,
            interactive=False,
            placeholder="对话历史将显示在这里..."
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        
        #### 🚀 快速开始
        1. **设置API密钥**: 在上方输入您的阿里云API密钥
        2. **选择方向**: 选择翻译方向（如"中文 → 英文"）
        3. **开始翻译**: 点击"开始翻译"按钮
        4. **开始说话**: 对着麦克风清晰说话
        5. **查看结果**: 实时查看识别和翻译结果
        6. **听取语音**: 自动播放翻译后的语音
        
        #### 💡 使用技巧
        - 说话时保持清晰，避免背景噪音
        - 等待语音播放完成后再继续说话
        - 定期点击"刷新结果"查看最新状态
        - 如遇问题，停止后重新开始
        """)
        
        # 事件绑定
        set_key_btn.click(
            fn=set_api_key,
            inputs=[api_key_input],
            outputs=[key_status]
        )
        
        start_btn.click(
            fn=start_translation,
            inputs=[language_pair],
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history]
        )
        
        refresh_btn.click(
            fn=refresh_results,
            outputs=[original_output, translated_output, audio_output, conversation_history]
        )
    
    return app

if __name__ == "__main__":
    print("🎯 启动稳定版实时翻译应用...")
    
    config_ok, msg = translator.check_config()
    if config_ok:
        print("✅ 配置检查通过")
    else:
        print(f"⚠️ 配置问题: {msg}")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7866,  # 使用新端口避免冲突
        share=False,
        show_error=True
    )