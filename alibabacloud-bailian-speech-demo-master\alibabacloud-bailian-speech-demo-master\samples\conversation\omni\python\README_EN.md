# Omni-Realtime Multimodal Large Model Example

English | [简体中文](./README.md)

This example demonstrates how to use the Omni-Realtime multimodal large model API, including audio interaction with interruption support and video interaction.

## Prerequisites

#### Configure Alibaba Cloud Bailian API-KEY
Before using the Bailian SDK to call the model, you need to create a model service on Alibaba Cloud and obtain an API-KEY.
- On the [Bailian console](https://bailian.console.aliyun.com/), find the API-KEY entry in the lower-left corner and click to enter the management page.
- Click 【Create new API-KEY】 to automatically generate an API-KEY for this account. The list displays the API-KEY ciphertext; click 【View】 to see the plaintext information of the API-KEY. Please save the plaintext information for future use.
- For more Bailian configuration details, please refer to: [PREREQUISITES.md](../../../../PREREQUISITES.md)

### 1. Audio Interaction Example
After running the audio interaction example, it will record your voice input via the microphone and play the audio returned by the Omni model through the speaker. You can interrupt the model's output at any time. To avoid self-interruption when playing the model's output, we recommend using headphones for the best experience.
```
python run_server_vad.py
```

### 2. Video Interaction Example
After running the video interaction example, it will record your voice input via the microphone and capture video via the camera. It will play the audio returned by the Omni model through the speaker.

You can interrupt the model's output at any time. To avoid self-interruption during playback of the model's output, we recommend using headphones for the best experience.

1. Run the service:
```
python run_with_camera.py
```
At this point, video capture has not started yet, and only voice interaction is active.

2. Open the webcam via the web frontend
After starting the interaction, double-click to open the `interface/index.html` page and grant permission to access the webcam. Click the 【Create connection】 button to send the video stream to the Python program and start transmission.

    You can open or close the connection in the web page at any time after initiating voice interaction to start or stop video transmission.

### 3. Non-server_vad Mode Example

This example simulates the mode without using `server_vad`, actively controlling the timing of model responses. Running the example will simulate multiple rounds of conversation, playing the audio generated by the large model through the speaker, and waiting until the previous response finishes playing before starting the next task.

```
python run_without_server_vad.py
```

### Notes on the Player

In `b64_pcm_player.py`, we implemented a streaming player that supports interruptions using pyaudio. The player's input is base 64 encoded PCM format audio.

There are two working threads in the player. One thread is responsible for reading base 64 encoded data and decoding it, while another thread is responsible for playing the audio in segments according to `chunk_size`. Audio segment playback is blocking, so a larger `chunk_size` may affect the delay of interruption. It is recommended to configure it as 100ms.
