# QWEN-OMNI实时多模态大模型
简体中文 | [English](./README_EN.md)

## 概述

Qwen-Omni 系列模型支持输入多种模态的数据，包括视频、音频、图片、文本，并输出音频与文本。实时 API 提供了低延迟的多模态交互能力，支持音频和视频的流式输入，并能够流式输出文本和音频。

## 功能特性

- **语音对话**: 实时语音和视频作为输入的多轮对话。
- **语音打断**: 支持在交互中任何时刻通过语音打断。
- **WebSocket 连接**: 低延迟实时通信。

## 前置条件

### 1. 获取 API 凭证
请登录多模态对话控制台，激活服务并创建应用，然后您可以获得以下参数：
- **API Key**: 用于身份验证
- **Workspace ID**: 工作空间标识符
- **App ID**: 应用标识符

### 2. 环境要求

#### Java 环境
- Java 8 或更高版本
- Maven 3.6+

#### Python 环境
- Python 3.9 或更高版本
- pip 包管理器

### 其他
更多 API 详情，请参考[官方文档](https://help.aliyun.com/zh/model-studio/realtime)。