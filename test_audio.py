"""
音频系统测试脚本
用于测试麦克风和扬声器是否正常工作
"""

import pyaudio
import numpy as np
import time
import threading
import wave

class AudioTester:
    """音频测试器"""
    
    def __init__(self):
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.format = pyaudio.paInt16
        self.channels = 1
        
    def list_audio_devices(self):
        """列出所有音频设备"""
        print("🔊 音频设备列表:")
        print("-" * 60)
        
        p = pyaudio.PyAudio()
        
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            print(f"设备 {i}: {info['name']}")
            print(f"  最大输入声道: {info['maxInputChannels']}")
            print(f"  最大输出声道: {info['maxOutputChannels']}")
            print(f"  默认采样率: {info['defaultSampleRate']}")
            print("-" * 60)
            
        # 显示默认设备
        try:
            default_input = p.get_default_input_device_info()
            print(f"默认输入设备: {default_input['name']}")
        except:
            print("❌ 未找到默认输入设备")
            
        try:
            default_output = p.get_default_output_device_info()
            print(f"默认输出设备: {default_output['name']}")
        except:
            print("❌ 未找到默认输出设备")
            
        p.terminate()
        
    def test_microphone(self, duration=5):
        """测试麦克风录音"""
        print(f"🎤 开始测试麦克风录音 ({duration}秒)...")
        print("请对着麦克风说话...")
        
        try:
            p = pyaudio.PyAudio()
            
            stream = p.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            frames = []
            max_volume = 0
            
            for i in range(int(self.sample_rate / self.chunk_size * duration)):
                data = stream.read(self.chunk_size)
                frames.append(data)
                
                # 计算音量
                audio_data = np.frombuffer(data, dtype=np.int16)
                volume = np.sqrt(np.mean(audio_data**2))
                max_volume = max(max_volume, volume)
                
                # 显示音量条
                bar_length = int(volume / 1000)
                bar = "█" * min(bar_length, 50)
                print(f"\r音量: {bar:<50} {volume:.0f}", end="")
                
            stream.stop_stream()
            stream.close()
            p.terminate()
            
            print(f"\n✅ 麦克风测试完成")
            print(f"最大音量: {max_volume:.0f}")
            
            # 保存录音文件
            with wave.open('test_recording.wav', 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(p.get_sample_size(self.format))
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(frames))
                
            print("录音已保存为 test_recording.wav")
            return True
            
        except Exception as e:
            print(f"\n❌ 麦克风测试失败: {e}")
            return False
            
    def test_speaker(self):
        """测试扬声器播放"""
        print("🔊 开始测试扬声器...")
        
        try:
            # 生成测试音频（440Hz正弦波）
            duration = 2  # 秒
            frequency = 440  # Hz
            
            t = np.linspace(0, duration, int(self.sample_rate * duration))
            audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
            audio_data = (audio_data * 32767).astype(np.int16)
            
            p = pyaudio.PyAudio()
            
            stream = p.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                output=True
            )
            
            print("播放测试音频...")
            stream.write(audio_data.tobytes())
            
            stream.stop_stream()
            stream.close()
            p.terminate()
            
            print("✅ 扬声器测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 扬声器测试失败: {e}")
            return False
            
    def test_echo(self, duration=10):
        """测试回声（同时录音和播放）"""
        print(f"🔄 开始回声测试 ({duration}秒)...")
        print("您说的话会延迟0.5秒后播放出来")
        
        try:
            p = pyaudio.PyAudio()
            
            # 输入流
            input_stream = p.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 输出流
            output_stream = p.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                output=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 延迟缓冲区（0.5秒）
            delay_frames = int(self.sample_rate * 0.5 / self.chunk_size)
            buffer = [b'\x00' * self.chunk_size * 2] * delay_frames
            
            print("开始回声测试，请说话...")
            
            for i in range(int(self.sample_rate / self.chunk_size * duration)):
                # 录音
                data = input_stream.read(self.chunk_size)
                
                # 播放延迟的音频
                output_stream.write(buffer[0])
                
                # 更新缓冲区
                buffer.pop(0)
                buffer.append(data)
                
            input_stream.stop_stream()
            input_stream.close()
            output_stream.stop_stream()
            output_stream.close()
            p.terminate()
            
            print("✅ 回声测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 回声测试失败: {e}")
            return False

def main():
    """主测试流程"""
    tester = AudioTester()
    
    print("🎵 音频系统测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 列出音频设备")
        print("2. 测试麦克风录音")
        print("3. 测试扬声器播放")
        print("4. 测试回声（录音+播放）")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            tester.list_audio_devices()
        elif choice == '2':
            tester.test_microphone()
        elif choice == '3':
            tester.test_speaker()
        elif choice == '4':
            tester.test_echo()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")