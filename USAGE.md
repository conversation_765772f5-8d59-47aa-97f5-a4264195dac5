# 使用指南

## 🚀 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Python 3.8 或更高版本
- 稳定的网络连接
- 麦克风和扬声器设备

### 2. 安装应用

#### Windows用户
```bash
# 双击运行
start.bat

# 或在命令行中运行
python install.py
```

#### Linux/macOS用户
```bash
# 给脚本执行权限
chmod +x start.sh

# 运行安装脚本
./start.sh

# 或手动安装
python3 install.py
```

### 3. 配置API密钥

1. 注册阿里云账号：https://www.aliyun.com/
2. 开通百炼服务：https://dashscope.console.aliyun.com/
3. 获取API密钥
4. 编辑项目根目录下的`.env`文件：
   ```
   DASHSCOPE_API_KEY=your_actual_api_key_here
   ```

### 4. 测试配置

运行测试脚本确保一切正常：
```bash
# 测试API连接
python test_api.py

# 测试音频系统
python test_audio.py
```

### 5. 启动应用

```bash
# 方式1：使用启动脚本（推荐）
python run_app.py

# 方式2：直接运行简化版
python simple_translator.py

# 方式3：运行完整版
python realtime_translator_app.py
```

## 📱 使用界面

### 主界面功能

1. **语言选择**：选择翻译的源语言和目标语言
2. **录音控制**：开始/停止录音按钮
3. **状态显示**：显示当前应用状态
4. **文本显示**：实时显示原文和译文
5. **语音播放**：自动播放翻译后的语音

### 操作步骤

1. 打开浏览器访问 `http://localhost:7860`
2. 选择合适的语言对（如"中文->英文"）
3. 点击"🎤 开始录音"按钮
4. 对着麦克风清晰地说话
5. 观察实时翻译结果
6. 听取翻译语音播放
7. 点击"⏹️ 停止录音"结束翻译

## 🎯 使用场景

### 旅游场景
- **酒店入住**：与前台沟通房间需求
- **餐厅点餐**：了解菜单和下单
- **购物砍价**：与商家讨价还价
- **问路导航**：询问路线和交通

### 商务场景
- **会议交流**：跨语言商务讨论
- **客户接待**：接待外国客户
- **产品介绍**：向海外客户介绍产品
- **合同谈判**：理解合同条款

### 学习场景
- **口语练习**：练习外语发音
- **听力训练**：提高听力理解能力
- **词汇学习**：学习新单词和短语
- **语法应用**：在实际对话中应用语法

## ⚙️ 高级设置

### 音频参数调整

如果遇到音频问题，可以在代码中调整以下参数：

```python
# 在 simple_translator.py 中
self.sample_rate = 16000    # 采样率
self.chunk_size = 3200      # 缓冲区大小
self.channels = 1           # 声道数
```

### 语言支持扩展

要添加新的语言对，修改语言配置：

```python
self.languages = {
    "中文->英文": "en",
    "中文->日文": "ja",
    "中文->韩文": "ko",
    "中文->法文": "fr",
    "中文->德文": "de",
    "中文->西班牙文": "es",
    # 添加新语言
    "中文->俄文": "ru",
    "中文->阿拉伯文": "ar",
}
```

### 模型参数优化

根据需要调整AI模型参数：

```python
# ASR模型
model='gummy-realtime-v1'

# TTS模型
model='cosyvoice-v2'
voice='longhua_v2'
```

## 🔧 故障排除

### 常见问题

#### 1. 麦克风无法录音
**症状**：点击开始录音后没有反应
**解决方案**：
- 检查麦克风是否连接正常
- 确认浏览器已授权麦克风权限
- 运行 `python test_audio.py` 测试音频设备

#### 2. API调用失败
**症状**：显示API错误信息
**解决方案**：
- 检查网络连接是否正常
- 确认API密钥配置正确
- 运行 `python test_api.py` 测试API连接
- 检查阿里云账户余额和服务状态

#### 3. 翻译结果不准确
**症状**：翻译内容与预期不符
**解决方案**：
- 说话时保持清晰和适中的语速
- 避免背景噪音干扰
- 使用标准普通话或英语
- 句子不要过长，适当停顿

#### 4. 语音播放问题
**症状**：翻译文本显示正常但无语音播放
**解决方案**：
- 检查扬声器或耳机连接
- 确认浏览器音频播放权限
- 检查系统音量设置
- 尝试刷新页面重新开始

#### 5. 应用启动失败
**症状**：运行脚本后出现错误
**解决方案**：
- 检查Python版本（需要3.8+）
- 重新安装依赖：`pip install -r requirements.txt`
- 检查防火墙设置
- 查看错误日志定位具体问题

### 性能优化

#### 降低延迟
- 使用有线网络连接
- 关闭其他占用带宽的应用
- 选择网络状况良好的时间使用

#### 提高准确性
- 在安静环境中使用
- 保持麦克风距离适中（15-30cm）
- 说话清晰，语速适中
- 避免方言和俚语

## 📞 技术支持

### 获取帮助

1. **查看日志**：应用运行时的错误信息
2. **运行测试**：使用测试脚本诊断问题
3. **查阅文档**：阅读阿里云百炼官方文档
4. **社区支持**：在GitHub提交Issue

### 联系方式

- GitHub Issues：提交bug报告和功能请求
- 阿里云工单：API相关问题
- 官方文档：https://help.aliyun.com/zh/model-studio/

### 更新日志

查看 `README.md` 文件了解最新更新内容和版本信息。