"""
真实的实时翻译应用
集成阿里云百炼语音大模型API
"""

import os
import time
import tempfile
import threading
import queue
import wave
from typing import Optional, Tuple
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class RealTimeTranslator:
    """真实的实时翻译器"""
    
    def __init__(self):
        self.init_api_key()
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.languages = {
            "中文->英文": "en",
            "中文->日文": "ja", 
            "中文->韩文": "ko",
            "中文->法文": "fr",
            "中文->德文": "de",
            "中文->西班牙文": "es"
        }
        
        # 翻译器相关
        self.translator = None
        self.pyaudio_obj = None
        self.audio_stream = None
        self.target_language = "en"
        
        # 结果缓存
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def check_config(self):
        """检查配置是否正确"""
        if not hasattr(dashscope, 'api_key') or not dashscope.api_key or dashscope.api_key == 'your_api_key_here':
            return False, "API密钥未配置，请编辑.env文件设置DASHSCOPE_API_KEY"
            
        return True, "配置正确"
        
    def start_recording(self, language_pair: str):
        """开始真实录音和翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None
            
        if self.is_recording:
            return "已在录音中...", self.current_original, self.current_translated, self.current_audio
            
        if language_pair not in self.languages:
            return "❌ 不支持的语言对", "", "", None
            
        self.is_recording = True
        self.target_language = self.languages[language_pair]
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
        # 清空队列
        while not self.audio_queue.empty():
            self.audio_queue.get()
        while not self.result_queue.empty():
            self.result_queue.get()
        
        # 启动录音线程
        self.record_thread = threading.Thread(target=self._record_worker, daemon=True)
        self.record_thread.start()
        
        # 启动翻译线程
        self.translate_thread = threading.Thread(target=self._translate_worker, daemon=True)
        self.translate_thread.start()
        
        return f"✅ 开始录音 - {language_pair}", "", "", None
        
    def stop_recording(self):
        """停止录音和翻译"""
        if not self.is_recording:
            return "未在录音中", self.current_original, self.current_translated, self.current_audio
            
        self.is_recording = False
        
        # 清理资源
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        if self.translator:
            try:
                self.translator.stop()
            except:
                pass
            self.translator = None
            
        return "✅ 录音已停止", self.current_original, self.current_translated, self.current_audio
        
    def _record_worker(self):
        """录音工作线程"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            print("🎤 开始录音...")
            while self.is_recording:
                try:
                    data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_queue.put(data)
                except Exception as e:
                    print(f"录音错误: {e}")
                    break
                    
        except Exception as e:
            print(f"录音初始化错误: {e}")
            self.result_queue.put(("error", f"录音初始化失败: {e}"))
            
    def _translate_worker(self):
        """翻译工作线程"""
        class TranslationCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.sentence_count = 0
                
            def on_open(self):
                print("🌐 翻译器已连接")
                
            def on_close(self):
                print("🌐 翻译器已断开")
                
            def on_error(self, message):
                print(f"❌ 翻译错误: {message}")
                self.parent.result_queue.put(("error", f"翻译错误: {message}"))
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    # 处理转录结果（原文）
                    if transcription_result and transcription_result.text:
                        original_text = transcription_result.text
                        self.parent.result_queue.put(("original", original_text))
                        
                    # 处理翻译结果
                    if translation_result:
                        translation = translation_result.get_translation(self.parent.target_language)
                        if translation and translation.text:
                            translated_text = translation.text
                            self.parent.result_queue.put(("translated", translated_text))
                            
                            # 如果句子结束，生成语音
                            if translation.is_sentence_end:
                                self.sentence_count += 1
                                print(f"📝 句子 {self.sentence_count} 完成: {translated_text}")
                                self._generate_speech(translated_text)
                                
                except Exception as e:
                    print(f"翻译回调错误: {e}")
                    
            def _generate_speech(self, text):
                """生成语音"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_open(self):
                            print("🔊 TTS连接已建立")
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            print("🔊 TTS合成完成")
                            if self.audio_data:
                                # 保存临时音频文件
                                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                temp_file.write(self.audio_data)
                                temp_file.close()
                                self.parent.result_queue.put(("audio", temp_file.name))
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                    
                    tts_callback = TTSCallback(self.parent)
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=tts_callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
        
        try:
            callback = TranslationCallback(self)
            
            # 创建实时翻译识别器
            self.translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.target_language],
                callback=callback,
            )
            
            self.translator.start()
            print(f"🚀 翻译器已启动，请求ID: {self.translator.get_last_request_id()}")
            
            # 持续发送音频数据
            while self.is_recording:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        self.translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频数据错误: {e}")
                    break
                    
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            self.result_queue.put(("error", f"翻译初始化失败: {e}"))
            
    def get_latest_results(self):
        """获取最新的翻译结果"""
        has_update = False
        
        while not self.result_queue.empty():
            try:
                result_type, content = self.result_queue.get_nowait()
                has_update = True
                
                if result_type == "original":
                    self.current_original = content
                elif result_type == "translated":
                    self.current_translated = content
                elif result_type == "audio":
                    self.current_audio = content
                elif result_type == "error":
                    print(f"错误: {content}")
                    
            except queue.Empty:
                break
                
        return self.current_original, self.current_translated, self.current_audio

# 全局翻译器实例
translator = RealTimeTranslator()

def start_translation(language_pair):
    """开始翻译"""
    return translator.start_recording(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_recording()

def update_results():
    """更新结果显示"""
    if translator.is_recording:
        return translator.get_latest_results()
    else:
        return translator.current_original, translator.current_translated, translator.current_audio

def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="实时语音翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型的**真实**实时翻译应用
        
        **功能**: ASR（语音识别）+ 翻译 + TTS（语音合成）
        """)
        
        # 配置状态显示
        config_ok, config_msg = translator.check_config()
        if config_ok:
            gr.Markdown("✅ **API配置正常** - 可以正常使用所有功能")
        else:
            gr.Markdown(f"❌ **配置问题**: {config_msg}")
        
        with gr.Row():
            language_choice = gr.Dropdown(
                choices=list(translator.languages.keys()),
                value="中文->英文",
                label="选择翻译语言对",
                interactive=True
            )
            
        with gr.Row():
            start_btn = gr.Button("🎤 开始录音翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 原文（实时识别）",
                    lines=8,
                    interactive=False,
                    placeholder="请点击开始录音，然后对着麦克风说话..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 译文（实时翻译）",
                    lines=8, 
                    interactive=False,
                    placeholder="翻译结果将实时显示在这里..."
                )
                
        audio_output = gr.Audio(
            label="🔊 翻译语音（自动播放）",
            interactive=False,
            autoplay=True
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        1. **确保配置**: API密钥已正确配置
        2. **选择语言**: 从下拉菜单选择翻译语言对
        3. **开始录音**: 点击"开始录音翻译"按钮
        4. **开始说话**: 对着麦克风清晰地说话
        5. **查看结果**: 实时查看识别和翻译结果
        6. **听取语音**: 系统会自动播放翻译后的语音
        7. **停止录音**: 点击"停止录音"结束翻译
        
        ### ⚠️ 重要提醒
        - 需要麦克风权限，请在浏览器中允许
        - 保持网络连接稳定
        - 说话时保持清晰，避免背景噪音
        - 每句话之间适当停顿，便于系统识别句子边界
        """)
        
        # 添加手动刷新按钮
        refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_choice],
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        refresh_btn.click(
            fn=update_results,
            outputs=[original_output, translated_output, audio_output]
        )
        
        # 使用Gradio的定时器功能
        timer = gr.Timer(1.0)  # 每1秒更新一次
        timer.tick(
            fn=update_results,
            outputs=[original_output, translated_output, audio_output]
        )
"""
真实的实时翻译应用
集成阿里云百炼语音大模型API
"""

import os
import time
import tempfile
import threading
import queue
import wave
from typing import Optional, Tuple
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class RealTimeTranslator:
    """真实的实时翻译器"""
    
    def __init__(self):
        self.init_api_key()
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.languages = {
            "中文->英文": "en",
            "中文->日文": "ja", 
            "中文->韩文": "ko",
            "中文->法文": "fr",
            "中文->德文": "de",
            "中文->西班牙文": "es"
        }
        
        # 翻译器相关
        self.translator = None
        self.pyaudio_obj = None
        self.audio_stream = None
        self.target_language = "en"
        
        # 结果缓存
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def check_config(self):
        """检查配置是否正确"""
        if not hasattr(dashscope, 'api_key') or not dashscope.api_key or dashscope.api_key == 'your_api_key_here':
            return False, "API密钥未配置，请编辑.env文件设置DASHSCOPE_API_KEY"
            
        return True, "配置正确"
        
    def start_recording(self, language_pair: str):
        """开始真实录音和翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None
            
        if self.is_recording:
            return "已在录音中...", self.current_original, self.current_translated, self.current_audio
            
        if language_pair not in self.languages:
            return "❌ 不支持的语言对", "", "", None
            
        self.is_recording = True
        self.target_language = self.languages[language_pair]
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        
        # 清空队列
        while not self.audio_queue.empty():
            self.audio_queue.get()
        while not self.result_queue.empty():
            self.result_queue.get()
        
        # 启动录音线程
        self.record_thread = threading.Thread(target=self._record_worker, daemon=True)
        self.record_thread.start()
        
        # 启动翻译线程
        self.translate_thread = threading.Thread(target=self._translate_worker, daemon=True)
        self.translate_thread.start()
        
        return f"✅ 开始录音 - {language_pair}", "", "", None
        
    def stop_recording(self):
        """停止录音和翻译"""
        if not self.is_recording:
            return "未在录音中", self.current_original, self.current_translated, self.current_audio
            
        self.is_recording = False
        
        # 清理资源
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        if self.translator:
            try:
                self.translator.stop()
            except:
                pass
            self.translator = None
            
        return "✅ 录音已停止", self.current_original, self.current_translated, self.current_audio
        
    def _record_worker(self):
        """录音工作线程"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            print("🎤 开始录音...")
            while self.is_recording:
                try:
                    data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_queue.put(data)
                except Exception as e:
                    print(f"录音错误: {e}")
                    break
                    
        except Exception as e:
            print(f"录音初始化错误: {e}")
            self.result_queue.put(("error", f"录音初始化失败: {e}"))
            
    def _translate_worker(self):
        """翻译工作线程"""
        class TranslationCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.sentence_count = 0
                
            def on_open(self):
                print("🌐 翻译器已连接")
                
            def on_close(self):
                print("🌐 翻译器已断开")
                
            def on_error(self, message):
                print(f"❌ 翻译错误: {message}")
                self.parent.result_queue.put(("error", f"翻译错误: {message}"))
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    # 处理转录结果（原文）
                    if transcription_result and transcription_result.text:
                        original_text = transcription_result.text
                        self.parent.result_queue.put(("original", original_text))
                        
                    # 处理翻译结果
                    if translation_result:
                        translation = translation_result.get_translation(self.parent.target_language)
                        if translation and translation.text:
                            translated_text = translation.text
                            self.parent.result_queue.put(("translated", translated_text))
                            
                            # 如果句子结束，生成语音
                            if translation.is_sentence_end:
                                self.sentence_count += 1
                                print(f"📝 句子 {self.sentence_count} 完成: {translated_text}")
                                self._generate_speech(translated_text)
                                
                except Exception as e:
                    print(f"翻译回调错误: {e}")
                    
            def _generate_speech(self, text):
                """生成语音"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_open(self):
                            print("🔊 TTS连接已建立")
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            print("🔊 TTS合成完成")
                            if self.audio_data:
                                # 保存临时音频文件
                                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                temp_file.write(self.audio_data)
                                temp_file.close()
                                self.parent.result_queue.put(("audio", temp_file.name))
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                    
                    tts_callback = TTSCallback(self.parent)
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=tts_callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
        
        try:
            callback = TranslationCallback(self)
            
            # 创建实时翻译识别器
            self.translator = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.target_language],
                callback=callback,
            )
            
            self.translator.start()
            print(f"🚀 翻译器已启动，请求ID: {self.translator.get_last_request_id()}")
            
            # 持续发送音频数据
            while self.is_recording:
                try:
                    if not self.audio_queue.empty():
                        audio_data = self.audio_queue.get()
                        self.translator.send_audio_frame(audio_data)
                    else:
                        time.sleep(0.01)
                except Exception as e:
                    print(f"发送音频数据错误: {e}")
                    break
                    
        except Exception as e:
            print(f"翻译工作线程错误: {e}")
            self.result_queue.put(("error", f"翻译初始化失败: {e}"))
            
    def get_latest_results(self):
        """获取最新的翻译结果"""
        has_update = False
        
        while not self.result_queue.empty():
            try:
                result_type, content = self.result_queue.get_nowait()
                has_update = True
                
                if result_type == "original":
                    self.current_original = content
                elif result_type == "translated":
                    self.current_translated = content
                elif result_type == "audio":
                    self.current_audio = content
                elif result_type == "error":
                    print(f"错误: {content}")
                    
            except queue.Empty:
                break
                
        return self.current_original, self.current_translated, self.current_audio

# 全局翻译器实例
translator = RealTimeTranslator()

def start_translation(language_pair):
    """开始翻译"""
    return translator.start_recording(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_recording()

def update_results():
    """更新结果显示"""
    if translator.is_recording:
        return translator.get_latest_results()
    else:
        return translator.current_original, translator.current_translated, translator.current_audio

def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="实时语音翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🌍 实时语音翻译器
        
        基于阿里云百炼语音大模型的**真实**实时翻译应用
        
        **功能**: ASR（语音识别）+ 翻译 + TTS（语音合成）
        """)
        
        # 配置状态显示
        config_ok, config_msg = translator.check_config()
        if config_ok:
            gr.Markdown("✅ **API配置正常** - 可以正常使用所有功能")
        else:
            gr.Markdown(f"❌ **配置问题**: {config_msg}")
        
        with gr.Row():
            language_choice = gr.Dropdown(
                choices=list(translator.languages.keys()),
                value="中文->英文",
                label="选择翻译语言对",
                interactive=True
            )
            
        with gr.Row():
            start_btn = gr.Button("🎤 开始录音翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 原文（实时识别）",
                    lines=8,
                    interactive=False,
                    placeholder="请点击开始录音，然后对着麦克风说话..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 译文（实时翻译）",
                    lines=8, 
                    interactive=False,
                    placeholder="翻译结果将实时显示在这里..."
                )
                
        audio_output = gr.Audio(
            label="🔊 翻译语音（自动播放）",
            interactive=False,
            autoplay=True
        )
        
        gr.Markdown("""
        ### 📋 使用说明
        1. **确保配置**: API密钥已正确配置
        2. **选择语言**: 从下拉菜单选择翻译语言对
        3. **开始录音**: 点击"开始录音翻译"按钮
        4. **开始说话**: 对着麦克风清晰地说话
        5. **查看结果**: 实时查看识别和翻译结果
        6. **听取语音**: 系统会自动播放翻译后的语音
        7. **停止录音**: 点击"停止录音"结束翻译
        
        ### ⚠️ 重要提醒
        - 需要麦克风权限，请在浏览器中允许
        - 保持网络连接稳定
        - 说话时保持清晰，避免背景噪音
        - 每句话之间适当停顿，便于系统识别句子边界
        """)
        
        # 事件绑定
        start_btn.click(
            fn=start_translation,
            inputs=[language_choice],
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output]
        )
        
        # 定时更新结果 - 使用简单的定时刷新
        def refresh_results():
            while True:
                time.sleep(0.5)  # 每0.5秒更新一次
                if translator.is_recording:
                    yield update_results()
                else:
                    yield translator.current_original, translator.current_translated, translator.current_audio
        
        # 创建一个隐藏的定时器
        refresh_btn = gr.Button("🔄 刷新", visible=False)
        refresh_btn.click(
            fn=update_results,
            outputs=[original_output, translated_output, audio_output]
        )
    
    return app

if __name__ == "__main__":
    print("🚀 启动真实翻译应用...")
    
    # 检查配置
    config_ok, msg = translator.check_config()
    if not config_ok:
        print(f"⚠️ 配置问题: {msg}")
        print("请编辑.env文件，设置正确的DASHSCOPE_API_KEY")
    else:
        print("✅ 配置检查通过")
        print(f"API密钥: {dashscope.api_key[:8]}...")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7861,  # 使用不同端口避免冲突
        share=False,
        show_error=True
    )