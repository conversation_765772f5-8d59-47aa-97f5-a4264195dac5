"""
修复版智能自动双向翻译应用
"""

import os
import time
import tempfile
import threading
import queue
from typing import Op<PERSON>, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *


class FixedSmartTranslator:
    """修复版智能双向翻译器"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # 语言配置
        self.language_pairs = {
            "中文 ⇄ 英文": {"A": "zh", "B": "en", "A_name": "中文", "B_name": "English"},
            "中文 ⇄ 日文": {"A": "zh", "B": "ja", "A_name": "中文", "B_name": "日本語"},
            "中文 ⇄ 韩文": {"A": "zh", "B": "ko", "A_name": "中文", "B_name": "한국어"},
        }
        
        # 当前语言对
        self.lang_A = "zh"
        self.lang_B = "en"
        self.lang_A_name = "中文"
        self.lang_B_name = "English"
        
        # 音频处理
        self.audio_queue = queue.Queue()
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer_A = None
        self.recognizer_B = None
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        self.silence_threshold = 800
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_direction = ""
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_smart_translation(self, language_pair: str):
        """开始智能双向翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "智能翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction
            
        if language_pair not in self.language_pairs:
            return "❌ 不支持的语言对", "", "", None, "", ""
            
        # 设置语言对
        current_pair = self.language_pairs[language_pair]
        self.lang_A = current_pair["A"]
        self.lang_B = current_pair["B"] 
        self.lang_A_name = current_pair["A_name"]
        self.lang_B_name = current_pair["B_name"]
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.conversation_history = []
        self.current_direction = "🤖 智能检测中..."
        
        # 启动智能翻译线程
        threading.Thread(target=self._smart_translation_worker, daemon=True).start()
        
        return f"✅ 开始智能翻译: {self.lang_A_name} ⇄ {self.lang_B_name}", "", "", None, "", "🤖 智能检测中..."
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ 智能翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        for recognizer in [self.recognizer_A, self.recognizer_B]:
            if recognizer:
                try:
                    recognizer.stop()
                except:
                    pass
        self.recognizer_A = None
        self.recognizer_B = None
            
    def _smart_translation_worker(self):
        """智能翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动双语言识别器
            self._start_dual_recognition()
            
        except Exception as e:
            print(f"智能翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            # 启动录音线程
            threading.Thread(target=self._smart_record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _smart_record_audio(self):
        """智能录音线程"""
        print("🎤 开始智能录音...")
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
                # 计算音频能量 - 修复版本
                try:
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    if len(audio_data) > 0:
                        # 安全的能量计算
                        audio_float = audio_data.astype(np.float32)
                        energy = np.sqrt(np.mean(audio_float * audio_float))
                    else:
                        energy = 0
                except:
                    energy = 0
                
                if energy > self.silence_threshold:
                    # 发送音频数据到识别器
                    self._send_to_recognizers(data)
                    
            except Exception as e:
                print(f"智能录音错误: {e}")
                break
                
    def _send_to_recognizers(self, audio_data):
        """发送音频数据到双识别器"""
        try:
            if self.recognizer_A:
                self.recognizer_A.send_audio_frame(audio_data)
            if self.recognizer_B:
                self.recognizer_B.send_audio_frame(audio_data)
        except Exception as e:
            print(f"发送音频到识别器错误: {e}")
            
    def _start_dual_recognition(self):
        """启动双语言识别器"""
        try:
            # 创建A语言识别器
            self._create_recognizer_A()
            
            # 创建B语言识别器  
            self._create_recognizer_B()
            
        except Exception as e:
            print(f"双识别器启动失败: {e}")
            
    def _create_recognizer_A(self):
        """创建A语言识别器"""
        class RecognizerACallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.lang_name = parent.lang_A_name
                
            def on_open(self):
                print(f"🌐 {self.lang_name}识别器已连接")
                
            def on_close(self):
                print(f"🌐 {self.lang_name}识别器已断开")
                
            def on_error(self, message):
                print(f"❌ {self.lang_name}识别错误: {message}")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    if transcription_result and transcription_result.text:
                        self.parent.current_original = transcription_result.text
                        self.parent.current_direction = f"{self.lang_name} → {self.parent.lang_B_name}"
                        print(f"[{self.lang_name}] 原文: {transcription_result.text}")
                        
                        # 处理翻译结果
                        if translation_result:
                            translation = translation_result.get_translation(self.parent.lang_B)
                            if translation and translation.text:
                                self.parent.current_translated = translation.text
                                print(f"[{self.lang_name}→{self.parent.lang_B_name}] 译文: {translation.text}")
                                
                                if translation.is_sentence_end:
                                    self._handle_translation_complete(
                                        transcription_result.text,
                                        translation.text,
                                        self.lang_name,
                                        self.parent.lang_B_name
                                    )
                                    
                except Exception as e:
                    print(f"{self.lang_name}识别事件处理错误: {e}")
                    
            def _handle_translation_complete(self, original, translated, source_lang, target_lang):
                """处理翻译完成"""
                # 添加到对话历史
                self.parent.conversation_history.append({
                    'timestamp': time.strftime('%H:%M:%S'),
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'original': original,
                    'translated': translated
                })
                
                # 生成语音
                self._generate_and_play_tts(translated)
                
            def _generate_and_play_tts(self, text):
                """生成并播放TTS"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = True
                                
                                try:
                                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                    temp_file.write(self.audio_data)
                                    temp_file.close()
                                    self.parent.current_audio = temp_file.name
                                    print(f"🔊 语音已生成")
                                    
                                    # 延迟恢复录音
                                    threading.Thread(target=self._reset_play_state, daemon=True).start()
                                    
                                except Exception as e:
                                    print(f"音频处理错误: {e}")
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = False
                                        
                        def _reset_play_state(self):
                            """重置播放状态"""
                            time.sleep(4)
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                            print("🔊 音频播放完成，恢复智能录音")
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                    
                    callback = TTSCallback(self.parent)
                    
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
                    with self.parent.audio_play_lock:
                        self.parent.is_playing_audio = False
        
        try:
            callback = RecognizerACallback(self)
            
            self.recognizer_A = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.lang_B],
                callback=callback,
            )
            
            self.recognizer_A.start()
            print(f"🚀 {self.lang_A_name}识别器已启动")
            
        except Exception as e:
            print(f"{self.lang_A_name}识别器启动失败: {e}")
            
    def _create_recognizer_B(self):
        """创建B语言识别器"""
        class RecognizerBCallback(TranslationRecognizerCallback):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.lang_name = parent.lang_B_name
                
            def on_open(self):
                print(f"🌐 {self.lang_name}识别器已连接")
                
            def on_close(self):
                print(f"🌐 {self.lang_name}识别器已断开")
                
            def on_error(self, message):
                print(f"❌ {self.lang_name}识别错误: {message}")
                
            def on_event(self, request_id, transcription_result, translation_result, usage):
                try:
                    if transcription_result and transcription_result.text:
                        self.parent.current_original = transcription_result.text
                        self.parent.current_direction = f"{self.lang_name} → {self.parent.lang_A_name}"
                        print(f"[{self.lang_name}] 原文: {transcription_result.text}")
                        
                        # 处理翻译结果
                        if translation_result:
                            translation = translation_result.get_translation(self.parent.lang_A)
                            if translation and translation.text:
                                self.parent.current_translated = translation.text
                                print(f"[{self.lang_name}→{self.parent.lang_A_name}] 译文: {translation.text}")
                                
                                if translation.is_sentence_end:
                                    self._handle_translation_complete(
                                        transcription_result.text,
                                        translation.text,
                                        self.lang_name,
                                        self.parent.lang_A_name
                                    )
                                    
                except Exception as e:
                    print(f"{self.lang_name}识别事件处理错误: {e}")
                    
            def _handle_translation_complete(self, original, translated, source_lang, target_lang):
                """处理翻译完成"""
                # 添加到对话历史
                self.parent.conversation_history.append({
                    'timestamp': time.strftime('%H:%M:%S'),
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'original': original,
                    'translated': translated
                })
                
                # 生成语音
                self._generate_and_play_tts(translated)
                
            def _generate_and_play_tts(self, text):
                """生成并播放TTS"""
                try:
                    class TTSCallback(ResultCallback):
                        def __init__(self, parent):
                            self.parent = parent
                            self.audio_data = b""
                            
                        def on_data(self, data: bytes):
                            self.audio_data += data
                            
                        def on_complete(self):
                            if self.audio_data:
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = True
                                
                                try:
                                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                    temp_file.write(self.audio_data)
                                    temp_file.close()
                                    self.parent.current_audio = temp_file.name
                                    print(f"🔊 语音已生成")
                                    
                                    # 延迟恢复录音
                                    threading.Thread(target=self._reset_play_state, daemon=True).start()
                                    
                                except Exception as e:
                                    print(f"音频处理错误: {e}")
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = False
                                        
                        def _reset_play_state(self):
                            """重置播放状态"""
                            time.sleep(4)
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                            print("🔊 音频播放完成，恢复智能录音")
                                
                        def on_error(self, message):
                            print(f"TTS错误: {message}")
                            with self.parent.audio_play_lock:
                                self.parent.is_playing_audio = False
                    
                    callback = TTSCallback(self.parent)
                    
                    synthesizer = SpeechSynthesizer(
                        model='cosyvoice-v2',
                        voice='longhua_v2',
                        callback=callback
                    )
                    synthesizer.call(text)
                    
                except Exception as e:
                    print(f"TTS生成失败: {e}")
                    with self.parent.audio_play_lock:
                        self.parent.is_playing_audio = False
        
        try:
            callback = RecognizerBCallback(self)
            
            self.recognizer_B = TranslationRecognizerRealtime(
                model='gummy-realtime-v1',
                format='pcm',
                sample_rate=self.sample_rate,
                transcription_enabled=True,
                translation_enabled=True,
                translation_target_languages=[self.lang_A],
                callback=callback,
            )
            
            self.recognizer_B.start()
            print(f"🚀 {self.lang_B_name}识别器已启动")
            
        except Exception as e:
            print(f"{self.lang_B_name}识别器启动失败: {e}")
            
    def _get_conversation_display(self):
        """获取对话历史显示"""
        if not self.conversation_history:
            return "🤖 智能对话历史将显示在这里...\n\n系统会自动检测语言并切换翻译方向，无需手动操作！"
            
        display_text = "🤖 智能对话记录:\n" + "="*50 + "\n\n"
        
        for i, conv in enumerate(self.conversation_history[-10:], 1):
            display_text += f"[{conv['timestamp']}] {conv['source_lang']} → {conv['target_lang']}\n"
            display_text += f"🎤 原文: {conv['original']}\n"
            display_text += f"🌐 译文: {conv['translated']}\n"
            display_text += "-" * 40 + "\n\n"
            
        return display_text
        
    def get_current_results(self):
        """获取当前结果"""
        return self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_direction

# 全局实例
translator = FixedSmartTranslator()

def set_api_key(api_key):
    """设置API密钥"""
    return translator.set_api_key(api_key)

def start_smart_translation(language_pair):
    """开始智能翻译"""
    return translator.start_smart_translation(language_pair)

def stop_translation():
    """停止翻译"""
    return translator.stop_translation()

def refresh_results():
    """刷新结果"""
    return translator.get_current_results()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="智能自动双向翻译", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🤖 智能自动双向翻译器
        
        **🎯 核心特色**: 全自动语言检测，无需手动切换方向！
        
        **✨ 智能功能**: 
        - 🧠 自动检测说话语言
        - 🔄 智能切换翻译方向  
        - 🎤 连续语音捕获
        - 🔊 防回环播放
        - 📝 完整对话记录
        
        **🌍 适用场景**: 面对面跨语言自然对话
        """)
        
        # API密钥设置区域
        with gr.Accordion("🔑 API密钥设置", open=not translator.api_key):
            gr.Markdown("""
            ### 🔗 获取API密钥
            1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
            2. 注册/登录阿里云账号
            3. 开通百炼服务（有免费额度）
            4. 创建API密钥
            5. 将密钥粘贴到下方输入框
            """)
            
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    type="password",
                    value=translator.api_key if translator.api_key else ""
                )
                set_key_btn = gr.Button("💾 保存密钥", variant="primary")
                
            key_status = gr.Textbox(
                label="设置状态",
                value="✅ API密钥已配置" if translator.api_key else "⚠️ 请设置API密钥",
                interactive=False
            )
        
        # 智能翻译设置区域
        with gr.Row():
            language_pair = gr.Dropdown(
                choices=list(translator.language_pairs.keys()),
                value="中文 ⇄ 英文",
                label="选择语言对",
                interactive=True
            )
        
        # 控制按钮
        with gr.Row():
            start_btn = gr.Button("🤖 开始智能翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止翻译", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
            
        status_display = gr.Textbox(
            label="状态",
            value="准备就绪",
            interactive=False
        )
        
        # 当前翻译方向显示
        direction_display = gr.Textbox(
            label="🧠 智能检测方向",
            value="等待开始...",
            interactive=False
        )
        
        # 实时翻译结果
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 当前原文",
                    lines=4,
                    interactive=False,
                    placeholder="说话时原文将自动显示..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🌐 当前译文",
                    lines=4,
                    interactive=False,
                    placeholder="翻译结果将自动显示..."
                )
        
        # 语音播放
        audio_output = gr.Audio(
            label="🔊 翻译语音（自动播放）",
            interactive=False,
            autoplay=True
        )
        
        # 智能对话历史
        conversation_history = gr.Textbox(
            label="🤖 智能对话历史",
            lines=12,
            interactive=False,
            placeholder="智能对话历史将显示在这里..."
        )
        
        gr.Markdown("""
        ### 🤖 智能翻译说明
        
        #### ✨ 核心优势
        - **🧠 全自动**: 无需手动切换，系统自动检测语言并翻译
        - **🎤 连续对话**: 持续监听，自然对话体验
        - **🔊 防回环**: 智能防止语音循环，播放时暂停录音
        - **📝 完整记录**: 自动记录完整对话历史
        
        #### 🚀 使用步骤
        1. **设置API密钥**: 在上方输入您的阿里云API密钥
        2. **选择语言对**: 选择需要翻译的语言对（如"中文 ⇄ 英文"）
        3. **开始翻译**: 点击"开始智能翻译"按钮
        4. **自然对话**: 
           - 🗣️ 直接对着麦克风说话（任意语言）
           - 🤖 系统自动检测语言并翻译
           - 🔊 自