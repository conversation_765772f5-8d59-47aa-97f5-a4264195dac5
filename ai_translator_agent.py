"""
AI翻译智能体
基于大模型的智能语音翻译助手
"""

import os
import time
import tempfile
import threading
import queue
from typing import Optional, Tuple, Dict
import gradio as gr
import dashscope
import pyaudio
import numpy as np
from dashscope.audio.asr import *
from dashscope.audio.tts_v2 import *
from dashscope import Generation


class AITranslatorAgent:
    """AI翻译智能体"""
    
    def __init__(self):
        self.api_key = ""
        self.init_api_key()
        
        # 翻译状态
        self.is_translating = False
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 1600
        self.format = pyaudio.paInt16
        self.channels = 1
        
        # AI智能体配置
        self.ai_model = "qwen-turbo"  # 使用通义千问
        self.translation_mode = "智能检测"
        self.context_memory = []  # 对话上下文记忆
        
        # 翻译模式配置
        self.translation_modes = {
            "智能检测": "自动检测语言并智能翻译",
            "中英互译": "中文和英文之间互相翻译",
            "多语言助手": "支持中文、英文、日文、韩文等多语言",
            "商务翻译": "专业商务场景翻译",
            "旅游助手": "旅游场景专用翻译"
        }
        
        # 音频处理
        self.pyaudio_obj = None
        self.audio_stream = None
        self.recognizer = None
        
        # 防回环机制
        self.is_playing_audio = False
        self.audio_play_lock = threading.Lock()
        
        # 结果存储
        self.conversation_history = []
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_analysis = ""
        
        # 音频缓冲
        self.audio_buffer = []
        self.buffer_size = 8
        
    def init_api_key(self):
        """初始化API密钥"""
        if 'DASHSCOPE_API_KEY' in os.environ:
            self.api_key = os.environ['DASHSCOPE_API_KEY']
            dashscope.api_key = self.api_key
        else:
            try:
                with open('.env', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('DASHSCOPE_API_KEY='):
                            key = line.split('=')[1].strip()
                            if key and key != 'your_api_key_here':
                                self.api_key = key
                                dashscope.api_key = key
                                break
            except FileNotFoundError:
                pass
                
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        if not api_key or api_key.strip() == "":
            return "❌ API密钥不能为空"
            
        self.api_key = api_key.strip()
        dashscope.api_key = self.api_key
        
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f"DASHSCOPE_API_KEY={self.api_key}\n")
            return "✅ API密钥设置成功并已保存"
        except Exception as e:
            return f"❌ 保存失败: {e}"
            
    def check_config(self):
        """检查配置"""
        if not self.api_key or self.api_key == 'your_api_key_here':
            return False, "请先设置API密钥"
        return True, "配置正常"
        
    def start_ai_translation(self, mode: str):
        """开始AI智能翻译"""
        config_ok, msg = self.check_config()
        if not config_ok:
            return f"❌ {msg}", "", "", None, "", ""
            
        if self.is_translating:
            return "AI翻译进行中...", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_analysis
            
        if mode not in self.translation_modes:
            return "❌ 不支持的翻译模式", "", "", None, "", ""
            
        # 设置翻译模式
        self.translation_mode = mode
        
        # 重置状态
        self.is_translating = True
        self.current_original = ""
        self.current_translated = ""
        self.current_audio = None
        self.current_analysis = ""
        self.conversation_history = []
        self.context_memory = []
        self.audio_buffer = []
        
        # 启动AI翻译线程
        threading.Thread(target=self._ai_translation_worker, daemon=True).start()
        
        return f"✅ 开始AI智能翻译: {mode}", "", "", None, "", f"🤖 AI模式: {self.translation_modes[mode]}"
        
    def stop_translation(self):
        """停止翻译"""
        if not self.is_translating:
            return "未在翻译中", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
            
        self.is_translating = False
        self._cleanup_resources()
        
        return "✅ AI翻译已停止", self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), ""
        
    def _cleanup_resources(self):
        """清理资源"""
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
            self.audio_stream = None
            
        if self.pyaudio_obj:
            try:
                self.pyaudio_obj.terminate()
            except:
                pass
            self.pyaudio_obj = None
            
        if self.recognizer:
            try:
                self.recognizer.stop()
            except:
                pass
            self.recognizer = None
            
    def _ai_translation_worker(self):
        """AI翻译工作线程"""
        try:
            # 启动音频录制
            self._start_audio_recording()
            
            # 启动语音识别
            self._start_speech_recognition()
            
        except Exception as e:
            print(f"AI翻译工作线程错误: {e}")
            self.is_translating = False
            
    def _start_audio_recording(self):
        """启动音频录制"""
        try:
            self.pyaudio_obj = pyaudio.PyAudio()
            
            print("🎤 初始化AI语音助手...")
            
            self.audio_stream = self.pyaudio_obj.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                input_device_index=None
            )
            
            print("🎤 AI语音助手已启动")
            
            # 启动录音线程
            threading.Thread(target=self._record_audio, daemon=True).start()
            
        except Exception as e:
            print(f"音频录制启动失败: {e}")
            
    def _record_audio(self):
        """录音线程"""
        print("🎤 AI助手开始监听...")
        
        while self.is_translating:
            try:
                # 检查是否正在播放音频
                with self.audio_play_lock:
                    if self.is_playing_audio:
                        time.sleep(0.1)
                        continue
                
                # 读取音频数据
                data = self.audio_stream.read(self.chunk_size, exception_on_overflow=False)
                
                # 检查音频质量
                audio_data = np.frombuffer(data, dtype=np.int16)
                if len(audio_data) > 0:
                    # 计算音频能量
                    energy = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
                    
                    # 只有足够的能量才发送
                    if energy > 80:  # 进一步降低阈值
                        self.audio_buffer.append(data)
                        
                        # 当缓冲区满时发送
                        if len(self.audio_buffer) >= self.buffer_size:
                            combined_data = b''.join(self.audio_buffer)
                            self._send_to_recognizer(combined_data)
                            self.audio_buffer = []
                    
            except Exception as e:
                print(f"录音错误: {e}")
                break
                
    def _send_to_recognizer(self, audio_data):
        """发送音频数据到识别器"""
        try:
            if self.recognizer and len(audio_data) > 0:
                self.recognizer.send_audio_frame(audio_data)
                print(f"📤 AI助手接收音频: {len(audio_data)} 字节")
        except Exception as e:
            print(f"发送音频到识别器错误: {e}")
            
    def _start_speech_recognition(self):
        """启动语音识别"""
        try:
            class AIRecognizerCallback(RecognitionCallback):
                def __init__(self, parent):
                    super().__init__()
                    self.parent = parent
                    
                def on_open(self):
                    print(f"🤖 AI语音识别器已连接")
                    
                def on_close(self):
                    print(f"🤖 AI语音识别器已断开")
                    
                def on_error(self, message):
                    print(f"❌ AI识别错误: {message}")
                    
                def on_event(self, request_id, transcription_result, usage):
                    try:
                        if transcription_result and transcription_result.text:
                            self.parent.current_original = transcription_result.text
                            print(f"[AI识别] 原文: {transcription_result.text}")
                            
                            # 使用AI进行智能翻译
                            if transcription_result.is_sentence_end:
                                self._handle_ai_translation(transcription_result.text)
                                        
                    except Exception as e:
                        print(f"AI识别事件处理错误: {e}")
                        
                def _handle_ai_translation(self, original_text):
                    """使用AI处理翻译"""
                    try:
                        # 构建AI提示词
                        prompt = self._build_ai_prompt(original_text)
                        
                        # 调用AI大模型
                        response = Generation.call(
                            model=self.parent.ai_model,
                            prompt=prompt,
                            temperature=0.3,
                            max_tokens=1000
                        )
                        
                        if response.status_code == 200:
                            ai_result = response.output.text.strip()
                            self._parse_ai_response(original_text, ai_result)
                        else:
                            print(f"AI调用失败: {response}")
                            
                    except Exception as e:
                        print(f"AI翻译处理错误: {e}")
                        
                def _build_ai_prompt(self, text):
                    """构建AI提示词"""
                    mode_prompts = {
                        "智能检测": f"""
你是一个智能翻译助手。请分析以下文本并完成翻译：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [翻译结果]
分析: [简短的语言和翻译分析]

翻译规则：
- 如果是中文，翻译成英文
- 如果是英文，翻译成中文  
- 如果是其他语言，翻译成中文
- 保持原文的语气和风格
""",
                        "中英互译": f"""
你是专业的中英文翻译助手。请翻译以下文本：

文本: "{text}"

请按以下格式回复：
语言: [中文/英文]
翻译: [翻译结果]
分析: [翻译要点说明]

翻译要求：
- 中文翻译成英文，英文翻译成中文
- 保持专业和准确
- 考虑语境和文化差异
""",
                        "多语言助手": f"""
你是多语言翻译专家。请处理以下文本：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [翻译成中文]
分析: [语言特点和翻译说明]

支持语言：中文、英文、日文、韩文、法文、德文、西班牙文等
""",
                        "商务翻译": f"""
你是专业商务翻译助手。请翻译以下商务文本：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [专业商务翻译]
分析: [商务用语特点说明]

翻译要求：
- 使用正式、专业的商务用语
- 准确传达商务含义
- 符合商务礼仪和习惯
""",
                        "旅游助手": f"""
你是旅游翻译助手。请翻译以下旅游相关文本：

文本: "{text}"

请按以下格式回复：
语言: [检测到的语言]
翻译: [实用旅游翻译]
分析: [旅游场景说明]

翻译要求：
- 简单易懂，便于交流
- 考虑旅游场景的实用性
- 提供友好、礼貌的表达
"""
                    }
                    
                    base_prompt = mode_prompts.get(self.parent.translation_mode, mode_prompts["智能检测"])
                    
                    # 添加上下文记忆
                    if self.parent.context_memory:
                        context = "\n".join(self.parent.context_memory[-3:])  # 最近3条对话
                        base_prompt += f"\n\n对话上下文：\n{context}"
                    
                    return base_prompt
                    
                def _parse_ai_response(self, original, ai_response):
                    """解析AI响应"""
                    try:
                        lines = ai_response.split('\n')
                        detected_lang = ""
                        translation = ""
                        analysis = ""
                        
                        for line in lines:
                            line = line.strip()
                            if line.startswith('语言:') or line.startswith('Language:'):
                                detected_lang = line.split(':', 1)[1].strip()
                            elif line.startswith('翻译:') or line.startswith('Translation:'):
                                translation = line.split(':', 1)[1].strip()
                            elif line.startswith('分析:') or line.startswith('Analysis:'):
                                analysis = line.split(':', 1)[1].strip()
                        
                        # 如果解析失败，使用整个响应作为翻译
                        if not translation:
                            translation = ai_response
                            
                        self.parent.current_translated = translation
                        self.parent.current_analysis = f"🧠 {detected_lang} | {analysis}" if analysis else f"🧠 AI智能翻译"
                        
                        print(f"[AI翻译] 语言: {detected_lang}")
                        print(f"[AI翻译] 译文: {translation}")
                        print(f"[AI分析] {analysis}")
                        
                        # 添加到对话历史
                        self.parent.conversation_history.append({
                            'timestamp': time.strftime('%H:%M:%S'),
                            'mode': self.parent.translation_mode,
                            'detected_lang': detected_lang,
                            'original': original,
                            'translated': translation,
                            'analysis': analysis
                        })
                        
                        # 添加到上下文记忆
                        self.parent.context_memory.append(f"原文: {original} | 译文: {translation}")
                        if len(self.parent.context_memory) > 10:
                            self.parent.context_memory.pop(0)
                        
                        # 生成语音
                        self._generate_and_play_tts(translation)
                        
                    except Exception as e:
                        print(f"AI响应解析错误: {e}")
                        
                def _generate_and_play_tts(self, text):
                    """生成并播放TTS"""
                    try:
                        class TTSCallback(ResultCallback):
                            def __init__(self, parent):
                                self.parent = parent
                                self.audio_data = b""
                                
                            def on_data(self, data: bytes):
                                self.audio_data += data
                                
                            def on_complete(self):
                                if self.audio_data:
                                    with self.parent.audio_play_lock:
                                        self.parent.is_playing_audio = True
                                    
                                    try:
                                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                                        temp_file.write(self.audio_data)
                                        temp_file.close()
                                        self.parent.current_audio = temp_file.name
                                        print(f"🔊 AI语音已生成")
                                        
                                        # 延迟恢复录音
                                        threading.Thread(target=self._reset_play_state, daemon=True).start()
                                        
                                    except Exception as e:
                                        print(f"音频处理错误: {e}")
                                        with self.parent.audio_play_lock:
                                            self.parent.is_playing_audio = False
                                            
                            def _reset_play_state(self):
                                """重置播放状态"""
                                time.sleep(3)
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = False
                                print("🔊 AI语音播放完成，继续监听")
                                    
                            def on_error(self, message):
                                print(f"TTS错误: {message}")
                                with self.parent.audio_play_lock:
                                    self.parent.is_playing_audio = False
                        
                        callback = TTSCallback(self.parent)
                        
                        synthesizer = SpeechSynthesizer(
                            model='cosyvoice-v2',
                            voice='longhua_v2',
                            callback=callback
                        )
                        synthesizer.call(text)
                        
                    except Exception as e:
                        print(f"TTS生成失败: {e}")
                        with self.parent.audio_play_lock:
                            self.parent.is_playing_audio = False
            
            callback = AIRecognizerCallback(self)
            
            # 使用正确的识别器类名
            self.recognizer = RecognitionRealtime(
                model='paraformer-realtime-v2',
                format='pcm',
                sample_rate=self.sample_rate,
                callback=callback,
            )
            
            self.recognizer.start()
            print(f"🚀 AI语音识别器已启动")
            
        except Exception as e:
            print(f"AI识别器启动失败: {e}")
            
    def _get_conversation_display(self):
        """获取对话历史显示"""
        if not self.conversation_history:
            return "🤖 AI智能对话历史将显示在这里...\n\n系统会自动检测语言并智能翻译，还会提供分析说明！"
            
        display_text = "🤖 AI智能对话记录:\n" + "="*60 + "\n\n"
        
        for i, conv in enumerate(self.conversation_history[-8:], 1):
            display_text += f"[{conv['timestamp']}] 模式: {conv['mode']}\n"
            display_text += f"🧠 检测语言: {conv.get('detected_lang', '未知')}\n"
            display_text += f"🎤 原文: {conv['original']}\n"
            display_text += f"🌐 译文: {conv['translated']}\n"
            if conv.get('analysis'):
                display_text += f"💡 分析: {conv['analysis']}\n"
            display_text += "-" * 50 + "\n\n"
            
        return display_text
        
    def get_current_results(self):
        """获取当前结果"""
        return self.current_original, self.current_translated, self.current_audio, self._get_conversation_display(), self.current_analysis

# 全局实例
ai_translator = AITranslatorAgent()

def set_api_key(api_key):
    """设置API密钥"""
    return ai_translator.set_api_key(api_key)

def start_ai_translation(mode):
    """开始AI翻译"""
    return ai_translator.start_ai_translation(mode)

def stop_translation():
    """停止翻译"""
    return ai_translator.stop_translation()

def refresh_results():
    """刷新结果"""
    return ai_translator.get_current_results()

def create_interface():
    """创建界面"""
    
    with gr.Blocks(title="AI翻译智能体", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🤖 AI翻译智能体
        
        **🧠 AI驱动**: 基于通义千问大模型的智能翻译助手
        
        **✨ 智能特色**: 
        - 🤖 AI自动语言检测
        - 🧠 智能翻译分析
        - 💭 上下文记忆
        - 🎯 多场景模式
        - 📝 详细分析说明
        
        **🌟 革命性体验**: AI理解语境，提供最佳翻译
        """)
        
        # API密钥设置区域
        with gr.Accordion("🔑 API密钥设置", open=not ai_translator.api_key):
            gr.Markdown("""
            ### 🔗 获取API密钥
            1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
            2. 注册/登录阿里云账号
            3. 开通百炼服务（有免费额度）
            4. 创建API密钥
            5. 将密钥粘贴到下方输入框
            """)
            
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    type="password",
                    value=ai_translator.api_key if ai_translator.api_key else ""
                )
                set_key_btn = gr.Button("💾 保存密钥", variant="primary")
                
            key_status = gr.Textbox(
                label="设置状态",
                value="✅ API密钥已配置" if ai_translator.api_key else "⚠️ 请设置API密钥",
                interactive=False
            )
        
        # AI翻译模式设置
        with gr.Row():
            translation_mode = gr.Dropdown(
                choices=list(ai_translator.translation_modes.keys()),
                value="智能检测",
                label="选择AI翻译模式",
                interactive=True
            )
        
        # 模式说明
        mode_description = gr.Textbox(
            label="模式说明",
            value=ai_translator.translation_modes["智能检测"],
            interactive=False
        )
        
        # 控制按钮
        with gr.Row():
            start_btn = gr.Button("🤖 启动AI翻译", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ 停止翻译", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 刷新结果", variant="secondary")
            
        status_display = gr.Textbox(
            label="状态",
            value="AI助手准备就绪",
            interactive=False
        )
        
        # AI分析显示
        analysis_display = gr.Textbox(
            label="🧠 AI智能分析",
            value="等待AI分析...",
            interactive=False
        )
        
        # 实时翻译结果
        with gr.Row():
            with gr.Column():
                original_output = gr.Textbox(
                    label="🎤 语音识别",
                    lines=4,
                    interactive=False,
                    placeholder="AI助手正在监听您的语音..."
                )
                
            with gr.Column():
                translated_output = gr.Textbox(
                    label="🤖 AI智能翻译",
                    lines=4,
                    interactive=False,
                    placeholder="AI翻译结果将显示在这里..."
                )
        
        # 语音播放
        audio_output = gr.Audio(
            label="🔊 AI语音合成",
            interactive=False,
            autoplay=True
        )
        
        # AI对话历史
        conversation_history = gr.Textbox(
            label="🤖 AI智能对话历史",
            lines=15,
            interactive=False,
            placeholder="AI智能对话历史将显示在这里..."
        )
        
        gr.Markdown("""
        ### 🤖 AI翻译智能体说明
        
        #### 🌟 AI核心优势
        - **🧠 智能理解**: AI大模型深度理解语言和语境
        - **🎯 场景适配**: 根据不同场景提供最佳翻译
        - **💭 上下文记忆**: 记住对话历史，提供连贯翻译
        - **📊 智能分析**: 提供语言特点和翻译要点分析
        - **🔄 自我优化**: 根据使用情况不断优化翻译质量
        
        #### 🎯 翻译模式详解
        - **智能检测**: AI自动检测语言并选择最佳翻译方向
        - **中英互译**: 专业的中英文双向翻译
        - **多语言助手**: 支持多种语言的智能翻译
        - **商务翻译**: 专业商务场景，使用正式商务用语
        - **旅游助手**: 旅游场景专用，简单实用易交流
        
        #### 🚀 使用步骤
        1. **设置API密钥**: 输入您的阿里云API密钥
        2. **选择AI模式**: 根据使用场景选择合适的翻译模式
        3. **启动AI助手**: 点击"启动AI翻译"
        4. **开始对话**: 
           - 🗣️ 对着麦克风说话（任意语言）
           - 🤖 AI自动识别语言和语境
           - 🧠 提供智能翻译和分析
           - 🔊 自动播放翻译语音
        5. **查看分析**: 在AI分析区域查看详细说明
        6. **对话记录**: 查看完整的AI对话历史
        
        #### 💡 AI使用技巧
        - AI会记住对话上下文，提供更准确的翻译
        - 不同模式适合不同场景，选择合适的模式效果更好
        - AI分析会告诉您语言特点和翻译要点
        - 定期查看对话历史，了解AI的翻译逻辑
        """)
        
        # 事件绑定
        set_key_btn.click(
            fn=set_api_key,
            inputs=[api_key_input],
            outputs=[key_status]
        )
        
        translation_mode.change(
            fn=lambda mode: ai_translator.translation_modes.get(mode, ""),
            inputs=[translation_mode],
            outputs=[mode_description]
        )
        
        start_btn.click(
            fn=start_ai_translation,
            inputs=[translation_mode],
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history, analysis_display]
        )
        
        stop_btn.click(
            fn=stop_translation,
            outputs=[status_display, original_output, translated_output, audio_output, conversation_history, analysis_display]
        )
        
        refresh_btn.click(
            fn=refresh_results,
            outputs=[original_output, translated_output, audio_output, conversation_history, analysis_display]
        )
    
    return app

if __name__ == "__main__":
    print("🤖 启动AI翻译智能体...")
    
    config_ok, msg = ai_translator.check_config()
    if config_ok:
        print("✅ AI配置检查通过")
    else:
        print(f"⚠️ 配置问题: {msg}")
    
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7867,  # 使用新端口
        share=False,
        show_error=True
    )
