"""
API连接测试脚本
用于测试阿里云百炼API是否配置正确
"""

import os
import sys
import dashscope
from dashscope.audio.tts_v2 import SpeechSynthesizer, ResultCallback
from dashscope.audio.asr import RecognitionRecognizer

def init_api_key():
    """初始化API密钥"""
    if 'DASHSCOPE_API_KEY' in os.environ:
        dashscope.api_key = os.environ['DASHSCOPE_API_KEY']
    else:
        try:
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('DASHSCOPE_API_KEY='):
                        key = line.split('=')[1].strip()
                        if key and key != 'your_api_key_here':
                            dashscope.api_key = key
                            break
        except FileNotFoundError:
            pass
    
    if not dashscope.api_key or dashscope.api_key == 'your_api_key_here':
        return False
    return True

def test_tts():
    """测试语音合成API"""
    print("🔊 测试语音合成API...")
    
    try:
        class TestCallback(ResultCallback):
            def __init__(self):
                self.success = False
                self.audio_data = b""
                
            def on_open(self):
                print("  - TTS连接已建立")
                
            def on_complete(self):
                print("  - TTS合成完成")
                self.success = True
                
            def on_error(self, message):
                print(f"  - TTS错误: {message}")
                
            def on_data(self, data: bytes):
                self.audio_data += data
                
        callback = TestCallback()
        synthesizer = SpeechSynthesizer(
            model='cosyvoice-v2',
            voice='longhua_v2',
            callback=callback
        )
        
        # 测试合成简单文本
        synthesizer.call("你好，这是语音合成测试。")
        
        if callback.success and len(callback.audio_data) > 0:
            print("✅ 语音合成API测试成功")
            print(f"  - 生成音频数据: {len(callback.audio_data)} 字节")
            return True
        else:
            print("❌ 语音合成API测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 语音合成API测试异常: {e}")
        return False

def test_asr():
    """测试语音识别API"""
    print("🎤 测试语音识别API...")
    
    try:
        # 使用示例音频文件进行测试
        sample_audio = "alibabacloud-bailian-speech-demo-master/alibabacloud-bailian-speech-demo-master/samples/sample-data/hello_world_male_16k_16bit_mono.wav"
        
        if not os.path.exists(sample_audio):
            print("  - 未找到示例音频文件，跳过ASR测试")
            return True
            
        recognizer = RecognitionRecognizer(
            model='paraformer-realtime-v2',
            format='wav',
            sample_rate=16000,
            callback=None
        )
        
        with open(sample_audio, 'rb') as f:
            audio_data = f.read()
            
        result = recognizer.call(audio_data)
        
        if result and hasattr(result, 'output') and result.output:
            print("✅ 语音识别API测试成功")
            print(f"  - 识别结果: {result.output.text}")
            return True
        else:
            print("❌ 语音识别API测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 语音识别API测试异常: {e}")
        return False

def test_translation():
    """测试翻译API"""
    print("🌐 测试翻译API...")
    
    try:
        from dashscope.audio.asr import TranslationRecognizer
        
        # 创建翻译识别器
        translator = TranslationRecognizer(
            model='gummy-v1',
            format='wav',
            sample_rate=16000,
            transcription_enabled=True,
            translation_enabled=True,
            translation_target_languages=['en']
        )
        
        # 使用示例音频文件
        sample_audio = "alibabacloud-bailian-speech-demo-master/alibabacloud-bailian-speech-demo-master/samples/sample-data/hello_world_male_16k_16bit_mono.wav"
        
        if not os.path.exists(sample_audio):
            print("  - 未找到示例音频文件，跳过翻译测试")
            return True
            
        with open(sample_audio, 'rb') as f:
            audio_data = f.read()
            
        result = translator.call(audio_data)
        
        if result and hasattr(result, 'output'):
            print("✅ 翻译API测试成功")
            if hasattr(result.output, 'transcription') and result.output.transcription:
                print(f"  - 转录结果: {result.output.transcription.text}")
            if hasattr(result.output, 'translation') and result.output.translation:
                translations = result.output.translation.get_translation_list()
                for lang, trans in translations.items():
                    print(f"  - 翻译到{lang}: {trans.text}")
            return True
        else:
            print("❌ 翻译API测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 翻译API测试异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🧪 阿里云百炼API连接测试")
    print("=" * 50)
    
    # 检查API密钥
    if not init_api_key():
        print("❌ API密钥未配置或无效")
        print("请检查.env文件中的DASHSCOPE_API_KEY设置")
        return False
    
    print(f"✅ API密钥已配置: {dashscope.api_key[:8]}...")
    print()
    
    # 测试各个API
    results = []
    
    # 测试TTS
    results.append(test_tts())
    print()
    
    # 测试ASR
    results.append(test_asr())
    print()
    
    # 测试翻译
    results.append(test_translation())
    print()
    
    # 总结结果
    print("=" * 50)
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print("🎉 所有API测试通过！")
        print("您可以正常使用实时翻译应用了")
        return True
    else:
        print(f"⚠️  {success_count}/{total_count} 个API测试通过")
        print("部分功能可能无法正常使用")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        sys.exit(1)