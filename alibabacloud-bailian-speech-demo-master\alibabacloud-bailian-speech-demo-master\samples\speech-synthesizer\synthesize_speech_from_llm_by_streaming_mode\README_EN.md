[comment]: # (title and brief introduction of the sample)
## Speech Synthesis of Real-Time LLM Output with Streaming Mode

English | [简体中文](./README.md)

This example demonstrates how to synthesize speech streams from text generated by a large language model (LLM) and play them through speakers in real-time.

[comment]: # (list of scenarios of the sample)
### :point_right: Applicable Scenarios

| Application Scenario | Typical Usage | Description |
| ----- | ----- | ----- |
| **Voice Broadcasting** | Information Broadcasting | *Real-time broadcasting of news summaries and information generated by LLMs* |
| **Call Center** | Agent Response Synthesis | *Generate customer service responses via LLMs and broadcast them in real-time* |
| **Digital Human** | News Broadcasting, Live Streaming, E-learning, Voice Chat | *Drive digital humans for news broadcasting, virtual live streaming, online education, language learning, and voice chat scenarios* |

[comment]: # (supported programming languages of the sample)
### :point_right: Supported Languages
- [Python](./python)
- [Java](./java)

[comment]: # (model and interface of the sample)
### :point_right: Technical Details
| Recommended Model | API Documentation |
| --- | --- |
| **cosyvoice-v1** | [CosyVoice LLM Speech Synthesis API](https://help.aliyun.com/zh/model-studio/developer-reference/api-details-25) <br> [Voice Tone List](https://help.aliyun.com/zh/model-studio/cosyvoice-java-sdk#95303fd00f0ge) |
| **cosyvoice-v2** | [CosyVoice LLM Speech Synthesis API](https://help.aliyun.com/zh/model-studio/developer-reference/api-details-25) <br> [Voice Tone List](https://help.aliyun.com/zh/model-studio/cosyvoice-java-sdk#da9ae03e5ek7b) |

### :point_right: Expected Results
When running this example, it will:
1. Call Alibaba Cloud Bailian's Qwen-Plus LLM to answer "How to cook tomato scrambled eggs?"
2. Use the `longhua_v2` voice tone
3. Stream the LLM response text for synthesis
4. Stream audio output and play through speakers

[comment]: # (technical support of the sample)
### :point_right: Technical Support
<img src="https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/group-en.png" width="400"/>